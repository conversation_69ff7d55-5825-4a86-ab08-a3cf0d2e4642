package com.smartcar.easylauncher.data.model.theme.api;

import java.util.List;

public class ThemeListModel {

    private Integer total;
    private List<RowsDTO> rows;
    private Integer code;
    private String msg;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<RowsDTO> getRows() {
        return rows;
    }

    public void setRows(List<RowsDTO> rows) {
        this.rows = rows;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static class RowsDTO {
        private String createBy;
        private String createTime;
        private String updateBy;
        private String updateTime;
        private Integer id;
        private String themeName;
        private String themeDescription;
        private Integer themeType;
        private String themeTypeName;
        private Integer downloadCount;
        private Integer heat;
        private Integer authorId;
        private String author;
        private String authorImg;
        private Integer price;
        private Integer isTrialEnabled;
        private Integer status;
        private Integer releaseStatus;
        private Integer isVip;
        private String label;
        private String coverImage;
        private Integer sortOrder;

        public String getCreateBy() {
            return createBy;
        }

        public void setCreateBy(String createBy) {
            this.createBy = createBy;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateBy() {
            return updateBy;
        }

        public void setUpdateBy(String updateBy) {
            this.updateBy = updateBy;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getThemeName() {
            return themeName;
        }

        public void setThemeName(String themeName) {
            this.themeName = themeName;
        }

        public String getThemeDescription() {
            return themeDescription;
        }

        public void setThemeDescription(String themeDescription) {
            this.themeDescription = themeDescription;
        }

        public Integer getThemeType() {
            return themeType;
        }

        public void setThemeType(Integer themeType) {
            this.themeType = themeType;
        }

        public String getThemeTypeName() {
            return themeTypeName;
        }

        public void setThemeTypeName(String themeTypeName) {
            this.themeTypeName = themeTypeName;
        }

        public Integer getDownloadCount() {
            return downloadCount;
        }

        public void setDownloadCount(Integer downloadCount) {
            this.downloadCount = downloadCount;
        }

        public Integer getHeat() {
            return heat;
        }

        public void setHeat(Integer heat) {
            this.heat = heat;
        }

        public Integer getAuthorId() {
            return authorId;
        }

        public void setAuthorId(Integer authorId) {
            this.authorId = authorId;
        }

        public String getAuthor() {
            return author;
        }

        public void setAuthor(String author) {
            this.author = author;
        }

        public String getAuthorImg() {
            return authorImg;
        }

        public void setAuthorImg(String authorImg) {
            this.authorImg = authorImg;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }

        public Integer getIsTrialEnabled() {
            return isTrialEnabled;
        }

        public void setIsTrialEnabled(Integer isTrialEnabled) {
            this.isTrialEnabled = isTrialEnabled;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getReleaseStatus() {
            return releaseStatus;
        }

        public void setReleaseStatus(Integer releaseStatus) {
            this.releaseStatus = releaseStatus;
        }

        public Integer getIsVip() {
            return isVip;
        }

        public void setIsVip(Integer isVip) {
            this.isVip = isVip;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getCoverImage() {
            return coverImage;
        }

        public void setCoverImage(String coverImage) {
            this.coverImage = coverImage;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }
    }
}
