package com.smartcar.easylauncher.data.database.dbmager;

import com.google.gson.Gson;
import com.smartcar.easylauncher.data.database.AppDatabase;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeResponse;

import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 简化的主题数据库管理器
 * 使用统一数据模型，无需转换
 */
public class SimplifiedThemeDbManager {
    private static final String TAG = "SimplifiedThemeDbManager";

    private SimplifiedThemeDbManager() {}

    public static SimplifiedThemeDbManager getInstance() {
        return Holder.INSTANCE;
    }

    private static class Holder {
        private static final SimplifiedThemeDbManager INSTANCE = new SimplifiedThemeDbManager();
    }

    /**
     * 直接保存主题 - 无需转换！
     */
    public Observable<Long> saveTheme(UnifiedThemeModel theme) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                // 直接保存，无需任何转换
                Long result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(theme);
                emitter.onNext(result);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 批量保存主题
     */
    public Observable<List<Long>> saveThemes(List<UnifiedThemeModel> themes) {
        return Observable.create((ObservableOnSubscribe<List<Long>>) emitter -> {
            try {
                List<Long> results = AppDatabase.getDatabase().getUnifiedThemeDao().insert(themes);
                emitter.onNext(results);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 根据ID获取主题
     */
    public Observable<UnifiedThemeModel> getThemeById(Long themeId) {
        return Observable.create((ObservableOnSubscribe<UnifiedThemeModel>) emitter -> {
            try {
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme != null) {
                    emitter.onNext(theme);
                } else {
                    emitter.onError(new Exception("主题不存在: " + themeId));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取所有主题
     */
    public Observable<List<UnifiedThemeModel>> getAllThemes() {
        return Observable.create((ObservableOnSubscribe<List<UnifiedThemeModel>>) emitter -> {
            try {
                List<UnifiedThemeModel> themes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();
                emitter.onNext(themes);
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 更新下载状态
     */
    public Observable<Integer> updateDownloadStatus(Long themeId, boolean isDownloaded, String localFilePath) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                // 先获取主题，更新状态，然后保存
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme != null) {
                    theme.setIsDownloaded(isDownloaded);
                    theme.setLocalFilePath(localFilePath);
                    theme.setDownloadTime(isDownloaded ? String.valueOf(System.currentTimeMillis()) : null);
                    int result = AppDatabase.getDatabase().getUnifiedThemeDao().update(theme);
                    emitter.onNext(result);
                } else {
                    emitter.onNext(0);
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 设置当前主题
     */
    public Observable<Integer> setCurrentTheme(Long themeId) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                // 清除所有主题的使用状态
                List<UnifiedThemeModel> allThemes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();
                for (UnifiedThemeModel theme : allThemes) {
                    if (theme.getIsCurrentTheme() != null && theme.getIsCurrentTheme()) {
                        theme.setIsCurrentTheme(false);
                        AppDatabase.getDatabase().getUnifiedThemeDao().update(theme);
                    }
                }

                // 设置指定主题为当前主题
                UnifiedThemeModel targetTheme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (targetTheme != null) {
                    targetTheme.setIsCurrentTheme(true);
                    int result = AppDatabase.getDatabase().getUnifiedThemeDao().update(targetTheme);
                    emitter.onNext(result);
                } else {
                    emitter.onNext(0);
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取当前使用的主题
     */
    public Observable<UnifiedThemeModel> getCurrentTheme() {
        return Observable.create((ObservableOnSubscribe<UnifiedThemeModel>) emitter -> {
            try {
                List<UnifiedThemeModel> allThemes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();
                UnifiedThemeModel currentTheme = null;
                for (UnifiedThemeModel theme : allThemes) {
                    if (theme.getIsCurrentTheme() != null && theme.getIsCurrentTheme()) {
                        currentTheme = theme;
                        break;
                    }
                }

                if (currentTheme != null) {
                    emitter.onNext(currentTheme);
                } else {
                    emitter.onError(new Exception("没有当前使用的主题"));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 从API响应JSON直接解析并保存
     */
    public Observable<Long> saveThemeFromJson(String jsonResponse) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                Gson gson = new Gson();

                // 解析API响应
                UnifiedThemeResponse response = gson.fromJson(jsonResponse, UnifiedThemeResponse.class);

                if (response.isSuccess() && response.getData() != null) {
                    // 直接保存，无需转换！
                    Long result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(response.getData());
                    emitter.onNext(result);
                } else {
                    emitter.onError(new Exception("API响应错误: " + response.getMsg()));
                }
                emitter.onComplete();
            } catch (Exception e) {
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }
}
