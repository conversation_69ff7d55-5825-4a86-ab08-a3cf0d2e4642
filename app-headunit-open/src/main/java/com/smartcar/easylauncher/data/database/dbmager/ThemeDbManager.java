package com.smartcar.easylauncher.data.database.dbmager;

import android.util.LruCache;

import com.google.gson.Gson;
import com.smartcar.easylauncher.data.database.AppDatabase;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeResponse;
import com.smartcar.easylauncher.shared.utils.MyLog;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * 完整的主题数据库管理器
 * 提供企业级的主题数据管理功能，包括：
 * 1. 数据验证和转换
 * 2. 业务逻辑处理
 * 3. 缓存管理
 * 4. 错误处理和恢复
 * 5. 主题生命周期管理
 * 6. 事务处理
 * 7. 性能优化
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class ThemeDbManager {
    private static final String TAG = "ThemeDbManager";
    
    // 缓存配置
    private static final int CACHE_SIZE = 50; // 缓存50个主题
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟过期
    
    // 内存缓存
    private final LruCache<Long, UnifiedThemeModel> themeCache;
    private final ConcurrentHashMap<Long, Long> cacheTimestamps;
    
    // 当前主题缓存
    private UnifiedThemeModel currentThemeCache;
    private long currentThemeCacheTime;
    
    private ThemeDbManager() {
        themeCache = new LruCache<Long, UnifiedThemeModel>(CACHE_SIZE) {
            @Override
            protected int sizeOf(Long key, UnifiedThemeModel theme) {
                // 估算主题对象的大小（字节）
                return estimateThemeSize(theme);
            }
        };
        cacheTimestamps = new ConcurrentHashMap<>();
    }

    public static ThemeDbManager getInstance() {
        return Holder.INSTANCE;
    }

    private static class Holder {
        private static final ThemeDbManager INSTANCE = new ThemeDbManager();
    }

    /**
     * 估算主题对象的内存大小
     */
    private int estimateThemeSize(UnifiedThemeModel theme) {
        if (theme == null) return 0;
        
        int size = 0;
        // 基础字段大小估算
        size += (theme.getThemeName() != null ? theme.getThemeName().length() * 2 : 0);
        size += (theme.getThemeDescription() != null ? theme.getThemeDescription().length() * 2 : 0);
        size += (theme.getAuthor() != null ? theme.getAuthor().length() * 2 : 0);
        size += (theme.getCoverImage() != null ? theme.getCoverImage().length() * 2 : 0);
        
        // 预览图片大小估算
        if (theme.getPreviewImages() != null) {
            size += theme.getPreviewImages().size() * 200; // 每个预览图片估算200字节
        }
        
        // 主题包大小估算
        if (theme.getThemePackage() != null) {
            size += 500; // 主题包信息估算500字节
        }
        
        return Math.max(size, 100); // 最小100字节
    }

    /**
     * 检查缓存是否过期
     */
    private boolean isCacheExpired(Long themeId) {
        Long timestamp = cacheTimestamps.get(themeId);
        if (timestamp == null) return true;
        return System.currentTimeMillis() - timestamp > CACHE_EXPIRE_TIME;
    }

    /**
     * 更新缓存
     */
    private void updateCache(UnifiedThemeModel theme) {
        if (theme != null && theme.getId() != null) {
            themeCache.put(theme.getId(), theme);
            cacheTimestamps.put(theme.getId(), System.currentTimeMillis());
        }
    }

    /**
     * 从缓存获取主题
     */
    private UnifiedThemeModel getFromCache(Long themeId) {
        if (themeId == null || isCacheExpired(themeId)) {
            return null;
        }
        return themeCache.get(themeId);
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        themeCache.evictAll();
        cacheTimestamps.clear();
        currentThemeCache = null;
        currentThemeCacheTime = 0;
        MyLog.d(TAG, "缓存已清除");
    }

    /**
     * 验证主题数据的完整性 - 宽松版本，只验证关键字段
     */
    private boolean validateThemeData(UnifiedThemeModel theme) {
        if (theme == null) {
            MyLog.w(TAG, "主题数据为空");
            return false;
        }

        if (theme.getId() == null) {
            MyLog.w(TAG, "主题ID为空");
            return false;
        }

        // 主题名称可以为空，使用默认值
        if (theme.getThemeName() == null || theme.getThemeName().trim().isEmpty()) {
            MyLog.w(TAG, "主题名称为空，将使用默认名称");
            theme.setThemeName("未命名主题_" + theme.getId());
        }

        MyLog.d(TAG, "主题数据验证通过: ID=" + theme.getId() + ", Name=" + theme.getThemeName());
        return true;
    }

    /**
     * 数据清理和格式化
     */
    private UnifiedThemeModel cleanAndFormatTheme(UnifiedThemeModel theme) {
        if (theme == null) return null;
        
        // 清理字符串字段
        if (theme.getThemeName() != null) {
            theme.setThemeName(theme.getThemeName().trim());
        }
        
        if (theme.getThemeDescription() != null) {
            theme.setThemeDescription(theme.getThemeDescription().trim());
        }
        
        if (theme.getAuthor() != null) {
            theme.setAuthor(theme.getAuthor().trim());
        }
        
        // 设置默认值 - 确保 NOT NULL 字段不为空
        if (theme.getIsDownloaded() == null) {
            theme.setIsDownloaded(false);
            MyLog.d(TAG, "设置默认下载状态: false");
        }

        if (theme.getIsCurrentTheme() == null) {
            theme.setIsCurrentTheme(false);
            MyLog.d(TAG, "设置默认当前主题状态: false");
        }
        
        if (theme.getDownloadCount() == null) {
            theme.setDownloadCount(0L);
        }
        
        if (theme.getHeat() == null) {
            theme.setHeat(0L);
        }
        
        return theme;
    }

    /**
     * 检查主题是否已存在
     */
    private boolean isThemeExists(Long themeId) {
        try {
            UnifiedThemeModel existingTheme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
            return existingTheme != null;
        } catch (Exception e) {
            MyLog.e(TAG, "检查主题是否存在时发生错误", e);
            return false;
        }
    }

    /**
     * 保存主题 - 完整版本，包含验证和业务逻辑
     */
    public Observable<Long> saveTheme(UnifiedThemeModel theme) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                MyLog.d(TAG, "开始保存主题: " + (theme != null ? theme.getThemeName() : "null"));

                // 打印详细的主题信息用于调试
                if (theme != null) {
                    MyLog.d(TAG, "主题详细信息:");
                    MyLog.d(TAG, "  - ID: " + theme.getId());
                    MyLog.d(TAG, "  - Name: " + theme.getThemeName());
                    MyLog.d(TAG, "  - Type: " + theme.getThemeType());
                    MyLog.d(TAG, "  - Author: " + theme.getAuthor());
                    MyLog.d(TAG, "  - Downloaded: " + theme.getIsDownloaded());
                    MyLog.d(TAG, "  - LocalPath: " + theme.getLocalFilePath());
                }

                // 1. 数据验证
                if (!validateThemeData(theme)) {
                    String errorMsg = "主题数据验证失败: " + (theme != null ? "ID=" + theme.getId() : "theme is null");
                    MyLog.e(TAG, errorMsg);
                    emitter.onError(new IllegalArgumentException(errorMsg));
                    return;
                }

                // 2. 数据清理和格式化
                UnifiedThemeModel cleanedTheme = cleanAndFormatTheme(theme);
                MyLog.d(TAG, "数据清理完成");

                // 3. 检查是否已存在，决定是插入还是更新
                boolean exists = isThemeExists(cleanedTheme.getId());
                Long result;

                if (exists) {
                    // 更新现有主题
                    MyLog.d(TAG, "主题已存在，执行更新操作");
                    result = updateExistingTheme(cleanedTheme);
                    MyLog.d(TAG, "更新操作完成，结果: " + result);
                } else {
                    // 插入新主题
                    MyLog.d(TAG, "主题不存在，执行插入操作");
                    result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(cleanedTheme);
                    MyLog.d(TAG, "插入操作完成，结果: " + result);
                }

                if (result == null || result <= 0) {
                    String errorMsg = "数据库操作返回无效结果: " + result;
                    MyLog.e(TAG, errorMsg);
                    emitter.onError(new RuntimeException(errorMsg));
                    return;
                }

                // 4. 更新缓存
                updateCache(cleanedTheme);
                MyLog.d(TAG, "缓存更新完成");

                // 5. 记录操作日志
                MyLog.v(TAG, "主题保存成功: ID=" + result + ", Name=" + cleanedTheme.getThemeName());

                emitter.onNext(result);
                emitter.onComplete();

            } catch (Exception e) {
                String errorMsg = "保存主题异常: " + e.getMessage();
                MyLog.e(TAG, errorMsg, e);
                emitter.onError(new RuntimeException(errorMsg, e));
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 更新现有主题，保留重要的本地状态
     */
    private Long updateExistingTheme(UnifiedThemeModel newTheme) throws Exception {
        try {
            // 获取现有主题
            UnifiedThemeModel existingTheme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(newTheme.getId());

            if (existingTheme != null) {
                // 保留重要的本地状态
                newTheme.setIsDownloaded(existingTheme.getIsDownloaded());
                newTheme.setLocalFilePath(existingTheme.getLocalFilePath());
                newTheme.setIsCurrentTheme(existingTheme.getIsCurrentTheme());
                newTheme.setDownloadTime(existingTheme.getDownloadTime());

                MyLog.d(TAG, "保留本地状态: downloaded=" + newTheme.getIsDownloaded() +
                        ", current=" + newTheme.getIsCurrentTheme());
            }

            // 执行更新
            int updateCount = AppDatabase.getDatabase().getUnifiedThemeDao().update(newTheme);
            MyLog.d(TAG, "更新主题完成，影响行数: " + updateCount);

            return newTheme.getId();

        } catch (Exception e) {
            MyLog.e(TAG, "更新现有主题失败", e);
            throw e;
        }
    }

    /**
     * 简化的保存方法 - 专门用于下载完成后的保存
     * 跳过复杂的验证和更新逻辑，直接插入或替换
     */
    public Observable<Long> saveDownloadedTheme(UnifiedThemeModel theme) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                MyLog.d(TAG, "开始保存下载的主题: " + (theme != null ? theme.getThemeName() : "null"));

                // 参数验证
                if (theme == null) {
                    String errorMsg = "主题数据为空，无法保存";
                    MyLog.e(TAG, errorMsg);
                    emitter.onError(new IllegalArgumentException(errorMsg));
                    return;
                }

                if (theme.getId() == null) {
                    String errorMsg = "主题ID为空，无法保存: " + theme.getThemeName();
                    MyLog.e(TAG, errorMsg);
                    emitter.onError(new IllegalArgumentException(errorMsg));
                    return;
                }

                // 打印主题详细信息用于调试
                MyLog.d(TAG, "主题详细信息: ID=" + theme.getId() +
                        ", Name=" + theme.getThemeName() +
                        ", Type=" + theme.getThemeType());

                // 设置基本的默认值
                if (theme.getThemeName() == null || theme.getThemeName().trim().isEmpty()) {
                    theme.setThemeName("主题_" + theme.getId());
                    MyLog.d(TAG, "设置默认主题名称: " + theme.getThemeName());
                }

                // 确保 NOT NULL 字段不为空
                if (theme.getIsDownloaded() == null) {
                    theme.setIsDownloaded(true); // 下载完成的主题
                    MyLog.d(TAG, "设置下载状态: true");
                }

                if (theme.getIsCurrentTheme() == null) {
                    theme.setIsCurrentTheme(false);
                    MyLog.d(TAG, "设置当前主题状态: false");
                }

                MyLog.d(TAG, "字段检查完成 - IsDownloaded: " + theme.getIsDownloaded() +
                        ", IsCurrentTheme: " + theme.getIsCurrentTheme());

                // 直接插入或替换，处理可能的迁移错误
                Long result;
                try {
                    result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(theme);
                } catch (Exception dbError) {
                    // 数据库操作失败，直接抛出异常
                    MyLog.e(TAG, "数据库操作失败: " + dbError.getMessage(), dbError);
                    throw dbError;
                }

                if (result != null && result > 0) {
                    // 更新缓存
                    updateCache(theme);
                    MyLog.v(TAG, "下载主题保存成功: ID=" + result + ", Name=" + theme.getThemeName());
                    emitter.onNext(result);
                } else {
                    MyLog.e(TAG, "数据库插入返回无效结果: " + result);
                    emitter.onError(new RuntimeException("数据库插入失败"));
                }

                emitter.onComplete();

            } catch (Exception e) {
                String errorMsg = "保存下载主题失败: " + e.getClass().getSimpleName() + " - " + e.getMessage();
                MyLog.e(TAG, errorMsg, e);

                // 提供更详细的错误信息
                if (e.getCause() != null) {
                    MyLog.e(TAG, "异常原因: " + e.getCause().getMessage());
                }

                emitter.onError(new RuntimeException(errorMsg, e));
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 最基本的保存方法 - 紧急备用方案
     * 只做最基本的插入，不做任何验证和处理
     */
    public Observable<Long> saveThemeBasic(UnifiedThemeModel theme) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                MyLog.d(TAG, "使用基本保存方法: " + (theme != null ? theme.getId() : "null"));

                // 严格的参数验证
                if (theme == null) {
                    String errorMsg = "基本保存方法: 主题数据为空";
                    MyLog.e(TAG, errorMsg);
                    emitter.onError(new IllegalArgumentException(errorMsg));
                    return;
                }

                if (theme.getId() == null) {
                    String errorMsg = "基本保存方法: 主题ID为空，主题名称: " + theme.getThemeName();
                    MyLog.e(TAG, errorMsg);
                    emitter.onError(new IllegalArgumentException(errorMsg));
                    return;
                }

                // 确保必需字段不为空
                if (theme.getIsDownloaded() == null) {
                    theme.setIsDownloaded(true);
                }
                if (theme.getIsCurrentTheme() == null) {
                    theme.setIsCurrentTheme(false);
                }

                MyLog.d(TAG, "基本保存 - 字段检查: IsDownloaded=" + theme.getIsDownloaded() +
                        ", IsCurrentTheme=" + theme.getIsCurrentTheme());

                // 直接调用数据库插入，处理可能的迁移错误
                Long result;
                try {
                    result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(theme);
                    MyLog.d(TAG, "基本保存完成，结果: " + result);
                } catch (Exception dbError) {
                    // 数据库操作失败，直接抛出异常
                    MyLog.e(TAG, "数据库操作失败: " + dbError.getMessage(), dbError);
                    throw dbError;
                }

                emitter.onNext(result != null ? result : theme.getId());
                emitter.onComplete();

            } catch (Exception e) {
                String errorMsg = "基本保存方法失败: " + e.getClass().getSimpleName() + " - " + e.getMessage();
                MyLog.e(TAG, errorMsg, e);

                // 提供更详细的错误信息
                if (e.getCause() != null) {
                    MyLog.e(TAG, "异常原因: " + e.getCause().getMessage());
                }

                emitter.onError(new RuntimeException(errorMsg, e));
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 测试数据库连接和基本操作
     */
    public Observable<Boolean> testDatabaseConnection() {
        return Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
            try {
                MyLog.d(TAG, "测试数据库连接");

                // 测试数据库是否可访问
                int count = AppDatabase.getDatabase().getUnifiedThemeDao().getThemeCount();
                MyLog.d(TAG, "当前数据库中主题数量: " + count);

                // 测试简单查询
                java.util.List<UnifiedThemeModel> themes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();
                MyLog.d(TAG, "查询到的主题数量: " + (themes != null ? themes.size() : 0));

                emitter.onNext(true);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "数据库连接测试失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 测试数据库连接并尝试修复 - 用于解决迁移问题
     */
    public Observable<Boolean> testAndFixDatabase() {
        return Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
            try {
                MyLog.d(TAG, "测试数据库连接并尝试修复");

                // 尝试简单的数据库操作来测试连接
                try {
                    AppDatabase db = AppDatabase.getDatabase();
                    int count = db.getUnifiedThemeDao().getThemeCount();
                    MyLog.d(TAG, "数据库连接正常，当前主题数量: " + count);
                    emitter.onNext(true);
                    emitter.onComplete();
                    return;
                } catch (Exception dbError) {
                    MyLog.w(TAG, "数据库连接异常，尝试修复: " + dbError.getMessage());
                }

                // 如果上面失败了，说明数据库有问题
                // 由于 fallbackToDestructiveMigration() 已经配置，Room 会自动处理
                // 我们只需要重新尝试获取数据库实例
                try {
                    // 等待一小段时间让系统处理
                    Thread.sleep(100);

                    AppDatabase db = AppDatabase.getDatabase();
                    int count = db.getUnifiedThemeDao().getThemeCount();
                    MyLog.d(TAG, "数据库修复成功，当前主题数量: " + count);

                    emitter.onNext(true);
                    emitter.onComplete();

                } catch (Exception retryError) {
                    MyLog.e(TAG, "数据库修复失败", retryError);
                    emitter.onError(retryError);
                }

            } catch (Exception e) {
                MyLog.e(TAG, "数据库测试和修复过程异常", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }



    /**
     * 批量保存主题 - 事务处理
     */
    public Observable<List<Long>> saveThemes(List<UnifiedThemeModel> themes) {
        return Observable.create((ObservableOnSubscribe<List<Long>>) emitter -> {
            try {
                MyLog.d(TAG, "开始批量保存主题，数量: " + (themes != null ? themes.size() : 0));

                if (themes == null || themes.isEmpty()) {
                    emitter.onNext(new java.util.ArrayList<>());
                    emitter.onComplete();
                    return;
                }

                // 数据验证和清理
                java.util.List<UnifiedThemeModel> validThemes = new java.util.ArrayList<>();
                for (UnifiedThemeModel theme : themes) {
                    if (validateThemeData(theme)) {
                        validThemes.add(cleanAndFormatTheme(theme));
                    } else {
                        MyLog.w(TAG, "跳过无效主题: " + (theme != null ? theme.getThemeName() : "null"));
                    }
                }

                if (validThemes.isEmpty()) {
                    MyLog.w(TAG, "没有有效的主题数据");
                    emitter.onNext(new java.util.ArrayList<>());
                    emitter.onComplete();
                    return;
                }

                // 批量插入（使用事务）
                java.util.List<Long> results = AppDatabase.getDatabase().getUnifiedThemeDao().insert(validThemes);

                // 更新缓存
                for (UnifiedThemeModel theme : validThemes) {
                    updateCache(theme);
                }

                MyLog.v(TAG, "批量保存主题成功，保存数量: " + results.size());

                emitter.onNext(results);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "批量保存主题失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 更新主题信息
     */
    public Observable<Integer> update(UnifiedThemeModel theme) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "更新主题信息: " + (theme != null ? theme.getThemeName() : "null"));

                if (theme == null || theme.getId() == null) {
                    emitter.onError(new IllegalArgumentException("主题或ID为空"));
                    return;
                }

                int result = AppDatabase.getDatabase().getUnifiedThemeDao().update(theme);
                MyLog.d(TAG, "主题更新完成，影响行数: " + result);

                // 清除缓存
                clearCache();

                emitter.onNext(result);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "更新主题失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 更新主题使用状态 - 专门的方法
     */
    public Observable<Integer> updateUsageStatus(Long themeId, boolean isUsing) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "更新主题使用状态: themeId=" + themeId + ", using=" + isUsing);

                if (themeId == null) {
                    emitter.onError(new IllegalArgumentException("主题ID不能为空"));
                    return;
                }

                // 获取现有主题
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme == null) {
                    MyLog.w(TAG, "主题不存在，无法更新使用状态: " + themeId);
                    emitter.onNext(0);
                    emitter.onComplete();
                    return;
                }

                // 更新使用状态
                theme.setUse(isUsing);
                theme.setIsCurrentTheme(isUsing);

                // 保存到数据库
                int result = AppDatabase.getDatabase().getUnifiedThemeDao().update(theme);

                // 更新缓存
                updateCache(theme);

                MyLog.v(TAG, "主题使用状态更新成功: " + theme.getThemeName() + " -> " + isUsing);

                emitter.onNext(result);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "更新主题使用状态失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 更新主题更新状态 - 专门的方法
     */
    public Observable<Integer> updateThemeUpdateStatus(Long themeId, boolean needUpdate) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "更新主题更新状态: themeId=" + themeId + ", needUpdate=" + needUpdate);

                if (themeId == null) {
                    emitter.onError(new IllegalArgumentException("主题ID不能为空"));
                    return;
                }

                // 获取现有主题
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme == null) {
                    MyLog.w(TAG, "主题不存在，无法更新更新状态: " + themeId);
                    emitter.onNext(0);
                    emitter.onComplete();
                    return;
                }

                // 注意：UnifiedThemeModel 可能没有 update 字段，这里需要根据实际情况调整
                // theme.setUpdate(needUpdate);

                // 保存到数据库
                int result = AppDatabase.getDatabase().getUnifiedThemeDao().update(theme);

                // 更新缓存
                updateCache(theme);

                MyLog.v(TAG, "主题更新状态更新成功: " + theme.getThemeName());

                emitter.onNext(result);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "更新主题更新状态失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 检查数据库是否可用 - 用于破坏性迁移后的验证
     */
    public Observable<Boolean> checkDatabaseReady() {
        return Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
            try {
                MyLog.d(TAG, "检查数据库是否可用");

                // 尝试简单的数据库操作
                AppDatabase db = AppDatabase.getDatabase();
                int count = db.getUnifiedThemeDao().getThemeCount();

                MyLog.d(TAG, "数据库检查成功，当前主题数量: " + count);
                emitter.onNext(true);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "数据库检查失败，可能正在重建中", e);
                emitter.onNext(false);
                emitter.onComplete();
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 根据ID获取主题 - 带缓存优化
     */
    public Observable<UnifiedThemeModel> getThemeById(Long themeId) {
        return Observable.create((ObservableOnSubscribe<UnifiedThemeModel>) emitter -> {
            try {
                MyLog.d(TAG, "获取主题: " + themeId);

                if (themeId == null) {
                    emitter.onError(new IllegalArgumentException("主题ID不能为空"));
                    return;
                }

                // 先尝试从缓存获取
                UnifiedThemeModel cachedTheme = getFromCache(themeId);
                if (cachedTheme != null) {
                    MyLog.d(TAG, "从缓存获取主题: " + cachedTheme.getThemeName());
                    emitter.onNext(cachedTheme);
                    emitter.onComplete();
                    return;
                }

                // 从数据库获取
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme != null) {
                    // 更新缓存
                    updateCache(theme);
                    MyLog.d(TAG, "从数据库获取主题: " + theme.getThemeName());
                    emitter.onNext(theme);
                } else {
                    MyLog.w(TAG, "主题不存在: " + themeId);
                    emitter.onError(new Exception("主题不存在: " + themeId));
                }
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "获取主题失败: " + themeId, e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取所有主题 - 带排序和过滤
     */
    public Observable<List<UnifiedThemeModel>> getAllThemes() {
        return Observable.create((ObservableOnSubscribe<List<UnifiedThemeModel>>) emitter -> {
            try {
                MyLog.d(TAG, "获取所有主题");

                List<UnifiedThemeModel> themes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();

                // 数据后处理
                if (themes != null) {
                    // 更新缓存
                    for (UnifiedThemeModel theme : themes) {
                        updateCache(theme);
                    }

                    MyLog.d(TAG, "获取主题成功，数量: " + themes.size());
                } else {
                    themes = new java.util.ArrayList<>();
                    MyLog.d(TAG, "没有找到主题数据");
                }

                emitter.onNext(themes);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "获取所有主题失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 更新下载状态 - 完整的状态管理
     */
    public Observable<Integer> updateDownloadStatus(Long themeId, boolean isDownloaded, String localFilePath) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "更新下载状态: themeId=" + themeId + ", downloaded=" + isDownloaded);

                if (themeId == null) {
                    emitter.onError(new IllegalArgumentException("主题ID不能为空"));
                    return;
                }

                // 获取现有主题
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme == null) {
                    MyLog.w(TAG, "主题不存在，无法更新下载状态: " + themeId);
                    emitter.onNext(0);
                    emitter.onComplete();
                    return;
                }

                // 更新状态
                theme.setIsDownloaded(isDownloaded);
                theme.setLocalFilePath(localFilePath);
                theme.setDownloadTime(isDownloaded ? String.valueOf(System.currentTimeMillis()) : null);

                // 保存到数据库
                int result = AppDatabase.getDatabase().getUnifiedThemeDao().update(theme);

                // 更新缓存
                updateCache(theme);

                MyLog.v(TAG, "下载状态更新成功: " + theme.getThemeName());

                emitter.onNext(result);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "更新下载状态失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 设置当前主题 - 完整的状态管理和验证
     */
    public Observable<Integer> setCurrentTheme(Long themeId) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "设置当前主题: " + themeId);

                if (themeId == null) {
                    emitter.onError(new IllegalArgumentException("主题ID不能为空"));
                    return;
                }

                // 验证主题是否存在且已下载
                UnifiedThemeModel targetTheme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (targetTheme == null) {
                    emitter.onError(new Exception("主题不存在: " + themeId));
                    return;
                }

                if (targetTheme.getIsDownloaded() == null || !targetTheme.getIsDownloaded()) {
                    emitter.onError(new Exception("主题未下载，无法设置为当前主题"));
                    return;
                }

                // 事务处理：清除所有主题的使用状态，然后设置新的当前主题
                AppDatabase.getDatabase().runInTransaction(() -> {
                    try {
                        // 1. 清除所有主题的使用状态
                        List<UnifiedThemeModel> allThemes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();
                        for (UnifiedThemeModel theme : allThemes) {
                            if (theme.getIsCurrentTheme() != null && theme.getIsCurrentTheme()) {
                                theme.setIsCurrentTheme(false);
                                AppDatabase.getDatabase().getUnifiedThemeDao().update(theme);
                                // 更新缓存
                                updateCache(theme);
                                MyLog.d(TAG, "清除主题使用状态: " + theme.getThemeName());
                            }
                        }

                        // 2. 设置新的当前主题
                        targetTheme.setIsCurrentTheme(true);
                        AppDatabase.getDatabase().getUnifiedThemeDao().update(targetTheme);

                        // 3. 更新缓存
                        updateCache(targetTheme);
                        currentThemeCache = targetTheme;
                        currentThemeCacheTime = System.currentTimeMillis();

                        MyLog.v(TAG, "当前主题设置成功: " + targetTheme.getThemeName());

                    } catch (Exception e) {
                        MyLog.e(TAG, "设置当前主题事务失败", e);
                        throw new RuntimeException(e);
                    }
                });

                emitter.onNext(1);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "设置当前主题失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取当前使用的主题 - 带缓存优化
     */
    public Observable<UnifiedThemeModel> getCurrentTheme() {
        return Observable.create((ObservableOnSubscribe<UnifiedThemeModel>) emitter -> {
            try {
                MyLog.d(TAG, "获取当前主题");

                // 检查缓存
                if (currentThemeCache != null &&
                    (System.currentTimeMillis() - currentThemeCacheTime) < CACHE_EXPIRE_TIME) {
                    MyLog.d(TAG, "从缓存获取当前主题: " + currentThemeCache.getThemeName());
                    emitter.onNext(currentThemeCache);
                    emitter.onComplete();
                    return;
                }

                // 从数据库查询
                List<UnifiedThemeModel> allThemes = AppDatabase.getDatabase().getUnifiedThemeDao().getAll();
                UnifiedThemeModel currentTheme = null;

                for (UnifiedThemeModel theme : allThemes) {
                    if (theme.getIsCurrentTheme() != null && theme.getIsCurrentTheme()) {
                        currentTheme = theme;
                        break;
                    }
                }

                if (currentTheme != null) {
                    // 更新缓存
                    currentThemeCache = currentTheme;
                    currentThemeCacheTime = System.currentTimeMillis();
                    updateCache(currentTheme);

                    MyLog.d(TAG, "获取当前主题成功: " + currentTheme.getThemeName());
                    emitter.onNext(currentTheme);
                } else {
                    MyLog.w(TAG, "没有设置当前主题");
                    emitter.onError(new Exception("没有当前使用的主题"));
                }
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "获取当前主题失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 从API响应JSON直接解析并保存 - 完整版本
     */
    public Observable<Long> saveThemeFromJson(String jsonResponse) {
        return Observable.create((ObservableOnSubscribe<Long>) emitter -> {
            try {
                MyLog.d(TAG, "从JSON保存主题");

                if (jsonResponse == null || jsonResponse.trim().isEmpty()) {
                    emitter.onError(new IllegalArgumentException("JSON响应为空"));
                    return;
                }

                Gson gson = new Gson();

                // 解析API响应
                UnifiedThemeResponse response = gson.fromJson(jsonResponse, UnifiedThemeResponse.class);

                if (response == null) {
                    emitter.onError(new Exception("JSON解析失败"));
                    return;
                }

                if (!response.isSuccess() || response.getData() == null) {
                    String errorMsg = "API响应错误: " + (response.getMsg() != null ? response.getMsg() : "未知错误");
                    emitter.onError(new Exception(errorMsg));
                    return;
                }

                // 直接保存主题，避免嵌套Observable
                UnifiedThemeModel theme = response.getData();
                MyLog.d(TAG, "解析主题成功: " + theme.getThemeName());

                // 数据验证
                if (!validateThemeData(theme)) {
                    emitter.onError(new IllegalArgumentException("主题数据验证失败"));
                    return;
                }

                // 数据清理和格式化
                UnifiedThemeModel cleanedTheme = cleanAndFormatTheme(theme);

                // 检查是否已存在，决定是插入还是更新
                boolean exists = isThemeExists(cleanedTheme.getId());
                Long result;

                if (exists) {
                    // 更新现有主题
                    MyLog.d(TAG, "主题已存在，执行更新操作");
                    result = updateExistingTheme(cleanedTheme);
                } else {
                    // 插入新主题
                    MyLog.d(TAG, "主题不存在，执行插入操作");
                    result = AppDatabase.getDatabase().getUnifiedThemeDao().insert(cleanedTheme);
                }

                // 更新缓存
                updateCache(cleanedTheme);

                MyLog.v(TAG, "从JSON保存主题成功: " + result);
                emitter.onNext(result);

            } catch (Exception e) {
                MyLog.e(TAG, "从JSON保存主题异常", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 删除主题 - 完整的清理流程
     */
    public Observable<Integer> deleteTheme(Long themeId) {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "删除主题: " + themeId);

                if (themeId == null) {
                    emitter.onError(new IllegalArgumentException("主题ID不能为空"));
                    return;
                }

                // 获取主题信息
                UnifiedThemeModel theme = AppDatabase.getDatabase().getUnifiedThemeDao().getById(themeId);
                if (theme == null) {
                    MyLog.w(TAG, "主题不存在: " + themeId);
                    emitter.onNext(0);
                    emitter.onComplete();
                    return;
                }

                // 检查是否为当前使用的主题
                if (theme.getIsCurrentTheme() != null && theme.getIsCurrentTheme()) {
                    emitter.onError(new Exception("无法删除当前使用的主题"));
                    return;
                }

                // 删除数据库记录
                int result = AppDatabase.getDatabase().getUnifiedThemeDao().deleteById(themeId);

                // 清除缓存
                themeCache.remove(themeId);
                cacheTimestamps.remove(themeId);

                // 如果是当前主题缓存，也清除
                if (currentThemeCache != null && themeId.equals(currentThemeCache.getId())) {
                    currentThemeCache = null;
                    currentThemeCacheTime = 0;
                }

                MyLog.v(TAG, "主题删除成功: " + theme.getThemeName());

                emitter.onNext(result);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "删除主题失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取已下载的主题列表
     */
    public Observable<List<UnifiedThemeModel>> getDownloadedThemes() {
        return Observable.create((ObservableOnSubscribe<List<UnifiedThemeModel>>) emitter -> {
            try {
                MyLog.d(TAG, "获取已下载主题列表");

                List<UnifiedThemeModel> downloadedThemes = AppDatabase.getDatabase().getUnifiedThemeDao().getDownloadedThemes();

                if (downloadedThemes != null) {
                    // 更新缓存
                    for (UnifiedThemeModel theme : downloadedThemes) {
                        updateCache(theme);
                    }
                    MyLog.d(TAG, "获取已下载主题成功，数量: " + downloadedThemes.size());
                } else {
                    downloadedThemes = new java.util.ArrayList<>();
                    MyLog.d(TAG, "没有已下载的主题");
                }

                emitter.onNext(downloadedThemes);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "获取已下载主题失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取已下载主题数量 - 直接SQL统计，高效专业
     */
    public Observable<Integer> getDownloadedThemeCount() {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "统计已下载主题数量");

                int count = AppDatabase.getDatabase().getUnifiedThemeDao().getDownloadedThemeCount();

                MyLog.d(TAG, "已下载主题数量: " + count);
                emitter.onNext(count);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "统计已下载主题数量失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取主题总数量 - 直接SQL统计
     */
    public Observable<Integer> getTotalThemeCount() {
        return Observable.create((ObservableOnSubscribe<Integer>) emitter -> {
            try {
                MyLog.d(TAG, "统计主题总数量");

                int count = AppDatabase.getDatabase().getUnifiedThemeDao().getThemeCount();

                MyLog.d(TAG, "主题总数量: " + count);
                emitter.onNext(count);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "统计主题总数量失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 获取主题统计信息 - 一次性获取所有统计数据
     */
    public Observable<ThemeStatistics> getThemeStatistics() {
        return Observable.create((ObservableOnSubscribe<ThemeStatistics>) emitter -> {
            try {
                MyLog.d(TAG, "获取主题统计信息");

                int totalCount = AppDatabase.getDatabase().getUnifiedThemeDao().getThemeCount();
                int downloadedCount = AppDatabase.getDatabase().getUnifiedThemeDao().getDownloadedThemeCount();

                ThemeStatistics statistics = new ThemeStatistics(totalCount, downloadedCount);

                MyLog.d(TAG, "主题统计: 总数=" + totalCount + ", 已下载=" + downloadedCount);
                emitter.onNext(statistics);
                emitter.onComplete();

            } catch (Exception e) {
                MyLog.e(TAG, "获取主题统计信息失败", e);
                emitter.onError(e);
            }
        }).subscribeOn(Schedulers.io())
          .observeOn(AndroidSchedulers.mainThread());
    }

    /**
     * 主题统计信息数据类
     */
    public static class ThemeStatistics {
        private final int totalCount;
        private final int downloadedCount;

        public ThemeStatistics(int totalCount, int downloadedCount) {
            this.totalCount = totalCount;
            this.downloadedCount = downloadedCount;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public int getDownloadedCount() {
            return downloadedCount;
        }

        public int getNotDownloadedCount() {
            return totalCount - downloadedCount;
        }
    }
}
