package com.smartcar.easylauncher.data.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;

import java.util.List;

import io.reactivex.rxjava3.core.Flowable;

/**
 * 统一主题数据访问对象
 * 简化的DAO，无需复杂的转换逻辑
 */
@Dao
public interface UnifiedThemeDao {

    // -------------------------插入操作--------------------------------------------

    /**
     * 插入单个主题（冲突时替换）
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Long insert(UnifiedThemeModel theme);

    /**
     * 批量插入主题
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insert(List<UnifiedThemeModel> themes);

    // -------------------------更新操作--------------------------------------------

    /**
     * 更新主题
     */
    @Update
    int update(UnifiedThemeModel theme);

    /**
     * 更新下载状态
     */
    @SuppressWarnings("RoomSqlValidation")
    @Query("UPDATE unified_themes SET is_downloaded = :isDownloaded, local_file_path = :localFilePath, download_time = :downloadTime WHERE id = :themeId")
    int updateDownloadStatus(Long themeId, boolean isDownloaded, String localFilePath, String downloadTime);

    /**
     * 设置当前主题
     */
    @SuppressWarnings("RoomSqlValidation")
    @Query("UPDATE unified_themes SET is_current_theme = :isCurrent WHERE id = :themeId")
    int setCurrentTheme(Long themeId, boolean isCurrent);

    /**
     * 清除所有主题的当前状态
     */
    @SuppressWarnings("RoomSqlValidation")
    @Query("UPDATE unified_themes SET is_current_theme = 0")
    void clearAllCurrentTheme();

    // -------------------------删除操作--------------------------------------------

    /**
     * 删除主题
     */
    @Delete
    int delete(UnifiedThemeModel theme);

    /**
     * 根据ID删除主题
     */
    @Query("DELETE FROM unified_themes WHERE id = :themeId")
    int deleteById(Long themeId);

    /**
     * 清空所有主题
     */
    @Query("DELETE FROM unified_themes")
    void deleteAll();

    // -------------------------查询操作--------------------------------------------

    /**
     * 根据ID查询主题
     */
    @Query("SELECT * FROM unified_themes WHERE id = :themeId")
    UnifiedThemeModel getById(Long themeId);

    /**
     * 查询所有主题
     */
    @Query("SELECT * FROM unified_themes ORDER BY create_time DESC")
    List<UnifiedThemeModel> getAll();

    /**
     * 查询所有主题（响应式）
     */
    @Query("SELECT * FROM unified_themes ORDER BY create_time DESC")
    Flowable<List<UnifiedThemeModel>> getAllFlowable();

    /**
     * 查询已下载的主题
     */
    @SuppressWarnings("RoomSqlValidation")
    @Query("SELECT * FROM unified_themes WHERE is_downloaded = 1 ORDER BY download_time DESC")
    List<UnifiedThemeModel> getDownloadedThemes();

    /**
     * 查询当前使用的主题
     */
    @SuppressWarnings("RoomSqlValidation")
    @Query("SELECT * FROM unified_themes WHERE is_current_theme = 1")
    UnifiedThemeModel getCurrentTheme();

    /**
     * 根据主题类型查询
     */
    @Query("SELECT * FROM unified_themes WHERE theme_type = :themeType ORDER BY heat DESC")
    List<UnifiedThemeModel> getByThemeType(int themeType);

    /**
     * 根据标签查询
     */
    @Query("SELECT * FROM unified_themes WHERE label = :label ORDER BY heat DESC")
    List<UnifiedThemeModel> getByLabel(String label);

    /**
     * 搜索主题（按名称和描述）
     */
    @Query("SELECT * FROM unified_themes WHERE theme_name LIKE :keyword OR theme_description LIKE :keyword ORDER BY heat DESC")
    List<UnifiedThemeModel> searchThemes(String keyword);

    /**
     * 查询热门主题
     */
    @Query("SELECT * FROM unified_themes ORDER BY heat DESC LIMIT :limit")
    List<UnifiedThemeModel> getHotThemes(int limit);

    /**
     * 查询最新主题
     */
    @Query("SELECT * FROM unified_themes ORDER BY create_time DESC LIMIT :limit")
    List<UnifiedThemeModel> getLatestThemes(int limit);

    /**
     * 统计主题数量
     */
    @Query("SELECT COUNT(*) FROM unified_themes")
    int getThemeCount();

    /**
     * 统计已下载主题数量
     */
    @SuppressWarnings("RoomSqlValidation")
    @Query("SELECT COUNT(*) FROM unified_themes WHERE is_downloaded = 1")
    int getDownloadedThemeCount();
}
