package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;


import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.shared.adapter.theme.ThemeBannerAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneLocalThemeDetailBinding;
import com.smartcar.easylauncher.data.database.dbmager.ThemeDbManager;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.data.model.theme.ThemeBannerModel;
import com.smartcar.easylauncher.data.model.theme.ThemeListModel;
import com.smartcar.easylauncher.data.model.theme.api.ApiResponse;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.shared.utils.file.FileUtils;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.shared.view.SecondaryPageActionBar;
import com.zhpan.bannerview.constants.IndicatorGravity;
import com.zhpan.bannerview.constants.PageStyle;
import com.zhpan.bannerview.utils.BannerUtils;

import com.zhpan.indicator.enums.IndicatorSlideMode;
import com.zhpan.indicator.enums.IndicatorStyle;

import android.annotation.SuppressLint;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import rxhttp.RxHttp;


/**
 * 主题详情Scene
 * 显示主题的详细信息，支持预览、下载、应用和删除操作
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class LocalThemeDetailScene extends BaseScene {

    private static final String TAG = "LocalThemeDetailScene";
    private static final String KEY_THEME_INFO = "theme_info";

    // 共享元素动画常量
    public static final String VIEW_NAME_THEME_IMAGE = "theme:detail:image";
    public static final String VIEW_NAME_THEME_TITLE = "theme:detail:title";
    
    private SceneLocalThemeDetailBinding binding;
    private UnifiedThemeModel themeInfo;
    private final CompositeDisposable disposables = new CompositeDisposable();
    private ThemeDbManager dbManager;
    private boolean isDownloading = false;

    // 统一操作栏组件
    private SecondaryPageActionBar actionBar;

    // 轮播图相关
    private ThemeBannerAdapter bannerAdapter;
    private int currentBannerItemCount = 0;

    // 操作状态管理
    private boolean isOperationInProgress = false;
    private long lastClickTime = 0;
    private static final long CLICK_DEBOUNCE_TIME = 1000; // 1秒防抖

    // 下载相关
    private Disposable downloadDisposable;
    private String downloadTimeMillis;

    // 版本更新相关
    private UnifiedThemeModel latestServerTheme;
    private boolean isCheckingUpdate = false;

    /**
     * 创建主题详情Scene实例
     */
    public static LocalThemeDetailScene newInstance(UnifiedThemeModel themeInfo) {
        LocalThemeDetailScene scene = new LocalThemeDetailScene();
        Bundle args = new Bundle();
        args.putString(KEY_THEME_INFO, new Gson().toJson(themeInfo));
        scene.setArguments(args);
        return scene;
    }

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SceneLocalThemeDetailBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initData();
        initViews();
        setupSharedElements();
        loadThemeData();

        // 检查主题更新（仅对非默认主题）
        if (themeInfo != null && themeInfo.getClassify() != null && themeInfo.getClassify() != 0 &&
            NetworkUtils.isNetworkConnected(requireSceneContext())) {
            checkThemeUpdate();
        }
    }

    /**
     * 设置共享元素动画
     */
    private void setupSharedElements() {
        if (themeInfo != null) {
            // 设置轮播图的共享元素名称
            ViewCompat.setTransitionName(binding.bannerViewPager, VIEW_NAME_THEME_IMAGE + themeInfo.getId());
            // 设置主题名称的共享元素名称
            ViewCompat.setTransitionName(binding.tvThemeName, VIEW_NAME_THEME_TITLE + themeInfo.getId());
        }
    }

    /**
     * 初始化数据
     */
    private void initData() {
        dbManager = ThemeDbManager.getInstance();

        // 从参数中获取主题信息
        Bundle args = getArguments();
        if (args != null) {
            String themeJson = args.getString(KEY_THEME_INFO);
            if (themeJson != null) {
                try {
                    themeInfo = new Gson().fromJson(themeJson, UnifiedThemeModel.class);
                } catch (Exception e) {
                    MyLog.e(TAG, "解析主题信息失败", e);
                }
            }
        }

        if (themeInfo == null) {
            MyLog.e(TAG, "主题信息为空，返回上一页");
            getNavigationScene(this).pop();
        }
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 初始化统一操作栏
        initActionBar();

        // 初始化轮播图
        initBannerView();
    }

    /**
     * 初始化统一操作栏
     */
    private void initActionBar() {
        actionBar = binding.actionBar;

        // 设置返回按钮
        actionBar.setBackAction(() -> getNavigationScene(this).pop());

        // 根据主题状态设置操作按钮
        updateActionBar();
    }

    /**
     * 加载主题数据 - 参考ThemeDetailScene的完整信息展示逻辑
     */
    private void loadThemeData() {
        if (themeInfo == null) return;

        // 设置主题基本信息（参考ThemeDetailScene的处理方式）
        setupThemeBasicInfo();
        
        // 设置主题详细信息（参考ThemeDetailScene的完整信息）
        setupThemeDetailedInfo();
        
        // 设置主题标签和推荐信息
        setupThemeLabelsAndRecommendation();
        
        // 设置版本信息（参考ThemeDetailScene的版本处理）
        setupThemeVersionInfo();
        
        // 加载主题轮播图
        loadThemeBanner();

        // 更新操作按钮
        updateActionBar();
    }

    /**
     * 设置主题基本信息 - 参考ThemeDetailScene的信息展示
     */
    private void setupThemeBasicInfo() {
        // 设置主题名称
        binding.tvThemeName.setText(themeInfo.getThemeName());
        
        // 设置作者信息（参考ThemeDetailScene的作者显示）
        String authorText = "作者: " + (themeInfo.getAuthor() != null ? themeInfo.getAuthor() : "未知");
        binding.tvThemeAuthor.setText(authorText);
        
        // 设置主题描述（参考ThemeDetailScene的描述处理）
        String description = themeInfo.getThemeDescription();
        if (description == null || description.trim().isEmpty()) {
            description = themeInfo.getThemeType() == 0 ? "精美的夜间主题，护眼舒适" : "清新的白天主题，简洁明快";
        }
        binding.tvThemeDescription.setText(description);
    }

    /**
     * 设置主题详细信息 - 简化版本，直接使用数据库数据
     */
    private void setupThemeDetailedInfo() {
        // 设置主题类型（参考ThemeDetailScene的类型显示）
        String themeType = themeInfo.getThemeTypeName();
        if (themeType == null || themeType.isEmpty()) {
            themeType = themeInfo.getThemeType() == 0 ? "夜间主题" : "白天主题";
        }
        binding.tvThemeType.setText(themeType);

        // 设置下载统计信息
        if (themeInfo.getId() != null && themeInfo.getId() < 0) {
            // 默认主题显示"系统内置"
            binding.tvDownloadCount.setText("系统内置主题");
        } else {
            // 用户主题显示下载量
            Long downloadCount = themeInfo.getDownloadCount() != null ? themeInfo.getDownloadCount() : 0L;
            binding.tvDownloadCount.setText("下载量: " + downloadCount);
        }
    }

    /**
     * 设置主题标签和推荐信息 - 简化版本，直接使用数据库标签
     */
    private void setupThemeLabelsAndRecommendation() {
        // 直接使用数据库中的标签，不自动生成
        if (themeInfo.getLabel() != null && !themeInfo.getLabel().trim().isEmpty()) {
            binding.tvThemeLabel.setText(themeInfo.getLabel());
            binding.tvThemeLabel.setVisibility(View.VISIBLE);
        } else {
            binding.tvThemeLabel.setVisibility(View.GONE);
        }
        
        // 设置推荐度显示（基于现有数据）
        setupRecommendationInfo();
    }

    /**
     * 设置推荐信息 - 简化版本，基于主题类型匹配
     */
    private void setupRecommendationInfo() {
        // 如果当前主题匹配时间模式，添加推荐提示
        boolean isNightMode = SkinManager.getInstance().isNightModeNow();
        boolean matchesCurrentMode = (themeInfo.getThemeType() == 0 && isNightMode) || 
                                   (themeInfo.getThemeType() == 1 && !isNightMode);
        
        if (matchesCurrentMode && !Boolean.TRUE.equals(themeInfo.getIsCurrentTheme())) {
            String currentLabel = binding.tvThemeLabel.getText().toString();
            String recommendText = "适合当前时间";
            
            if (!currentLabel.isEmpty()) {
                binding.tvThemeLabel.setText(currentLabel + " · " + recommendText);
            } else {
                binding.tvThemeLabel.setText(recommendText);
                binding.tvThemeLabel.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * 设置版本信息 - 参考ThemeDetailScene的版本处理逻辑
     */
    private void setupThemeVersionInfo() {
        // 检查是否有主题包信息来显示版本详情
        if (themeInfo.getThemePackage() != null) {
            String versionName = themeInfo.getThemePackage().getVersionName();
            if (versionName != null && !versionName.trim().isEmpty()) {
                binding.tvThemeVersion.setText("版本: v" + versionName);
            } else {
                binding.tvThemeVersion.setText("版本: 1.0");
            }
            
            // 检查是否有更新（参考ThemeDetailScene的更新检查）
            if (hasUpdate()) {
                binding.tvUpdateHint.setText("有新版本");
                binding.tvUpdateHint.setVisibility(View.VISIBLE);
            } else {
                binding.tvUpdateHint.setVisibility(View.GONE);
            }
            
            showUpdateInfo(); // 显示版本信息
        } else {
            // 没有主题包信息时显示默认版本
            binding.tvThemeVersion.setText("版本: 已安装");
            binding.tvUpdateHint.setVisibility(View.GONE);
        }
    }

    /**
     * 显示更新信息 - 优化版本，区分有更新和无更新的情况
     */
    private void showUpdateInfo() {
        // 检查主题信息和主题包是否存在
        if (themeInfo == null || themeInfo.getThemePackage() == null) {
            binding.updateInfoContainer.setVisibility(View.GONE);
            return;
        }

        UnifiedThemeModel.ThemePackage themePackage = themeInfo.getThemePackage();

        // 检查是否有版本信息需要显示
        boolean hasVersionInfo = (themePackage.getVersionName() != null && !themePackage.getVersionName().trim().isEmpty()) ||
                                (themePackage.getUpdateDescription() != null && !themePackage.getUpdateDescription().trim().isEmpty());

        if (!hasVersionInfo) {
            binding.updateInfoContainer.setVisibility(View.GONE);
            return;
        }

        binding.updateInfoContainer.setVisibility(View.VISIBLE);

        // 设置标题和版本文案
        String titleText = "版本信息";
        String versionText;

        if (themePackage.getVersionName() != null && !themePackage.getVersionName().trim().isEmpty()) {
            versionText = "当前版本 v" + themePackage.getVersionName();
        } else {
            versionText = "版本信息";
        }

        // 设置标题和版本文本
        binding.tvUpdateTitle.setText(titleText);
        binding.tvUpdateVersion.setText(versionText);

        // 隐藏NEW标签（因为这是当前版本信息，不是更新）
        binding.tvUpdateBadge.setVisibility(View.GONE);

        // 设置版本描述内容
        String updateContent = themePackage.getUpdateDescription();
        if (updateContent != null && !updateContent.trim().isEmpty()) {
            // 将更新内容格式化为美观的列表
            String formattedContent = formatUpdateContent(updateContent);
            binding.tvUpdateContent.setText(formattedContent);
        } else {
            // 显示默认的版本信息
            StringBuilder defaultContent = new StringBuilder();
            defaultContent.append("• 版本号: ").append(themePackage.getVersionName() != null ? themePackage.getVersionName() : "未知").append("\n");
            if (themePackage.getFileSize() != null) {
                defaultContent.append("• 文件大小: ").append(formatFileSize(themePackage.getFileSize())).append("\n");
            }
            if (themePackage.getPublishTime() != null) {
                defaultContent.append("• 发布时间: ").append(themePackage.getPublishTime()).append("\n");
            }
            defaultContent.append("• 感谢您的使用");
            binding.tvUpdateContent.setText(defaultContent.toString());
        }

        MyLog.d(TAG, "显示版本信息 - 版本: " + themePackage.getVersionName() +
                ", 描述: " + themePackage.getUpdateDescription());
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize <= 0) {
            return "未知";
        }

        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }

    /**
     * 格式化更新内容为美观的列表形式
     */
    private String formatUpdateContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        // 如果内容已经包含项目符号，直接返回
        if (content.contains("•") || content.contains("·") || content.contains("-")) {
            return content;
        }

        // 按行分割并添加项目符号
        String[] lines = content.split("\n");
        StringBuilder formatted = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty()) {
                // 移除可能存在的数字序号
                line = line.replaceFirst("^\\d+\\.\\s*", "");
                formatted.append("• ").append(line);
                if (i < lines.length - 1) {
                    formatted.append("\n");
                }
            }
        }

        return formatted.toString();
    }

    /**
     * 初始化轮播图
     */
    private void initBannerView() {
        bannerAdapter = new ThemeBannerAdapter();

        // 设置轮播图配置
        setupBannerView();
    }

    /**
     * 设置轮播图动态配置 - 根据图片数量
     */
    private void setupBannerBasicConfig(int itemCount) {

        try {
            // 根据图片数量设置自动播放和循环
            if (itemCount <= 1) {
                binding.bannerViewPager.setAutoPlay(false);
                binding.bannerViewPager.setCanLoop(false);
                binding.bannerViewPager.setIndicatorVisibility(View.GONE); // 单张图片隐藏指示器
                MyLog.d(TAG, "单张图片，禁用自动播放和循环，隐藏指示器");
            } else {
                binding.bannerViewPager.setAutoPlay(true);
                binding.bannerViewPager.setCanLoop(true);
                binding.bannerViewPager.setInterval(4000);
                binding.bannerViewPager.setIndicatorVisibility(View.VISIBLE); // 多张图片显示指示器
                MyLog.d(TAG, "多张图片，启用自动播放和循环，显示指示器");
            }

            MyLog.d(TAG, "轮播图动态配置完成，图片数量: " + itemCount);
        } catch (Exception e) {
            MyLog.e(TAG, "设置轮播图动态配置失败", e);
        }
    }



    /**
     * 设置轮播图配置 - 根据官方API优化
     */
    private void setupBannerView() {
        try {
            binding.bannerViewPager
                    // 基本配置
                    .setAdapter(bannerAdapter)
                    // 移除不存在的方向设置，binding.bannerViewPager默认就是水平方向
                    .setUserInputEnabled(true)
                    .setScrollDuration(400)

                    // 页面样式
                    .setPageStyle(PageStyle.NORMAL)
                    .setRevealWidth(0)
                    .setPageMargin(0)

                    // 指示器配置 - 现代化长条样式
                    .setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                    .setIndicatorSlideMode(IndicatorSlideMode.WORM)
                    .setIndicatorGravity(IndicatorGravity.CENTER)
                    .setIndicatorSliderWidth(BannerUtils.dp2px(8), BannerUtils.dp2px(24)) // 普通8dp，选中24dp长条
                    .setIndicatorSliderGap(BannerUtils.dp2px(4))
                    .setIndicatorSliderRadius(BannerUtils.dp2px(4))
                    .setIndicatorSliderColor(
                        Color.parseColor("#333333"),     // 选中：白色长条
                        Color.parseColor("#4DFFFFFF")    // 未选中：30%透明白色小点
                    )
                    .setIndicatorVisibility(View.VISIBLE)
                    .showIndicatorWhenOneItem(false)
                    .setIndicatorMargin(0, 0, 0, BannerUtils.dp2px(20)) // 底部边距20dp

                    // 轮播行为 - 初始设置为不自动播放
                    .setAutoPlay(false)
                    .setCanLoop(false)
                    .setInterval(4000)
                    .stopLoopWhenDetachedFromWindow(true)

                    // 生命周期 - 使用项目中实际存在的方法
                    .registerLifecycleObserver(getLifecycle())

                    // 点击监听
                    .setOnPageClickListener((view, position) -> {
                        MyLog.d(TAG, "点击轮播图第" + position + "张");
                        onBannerImageClick(position);
                    });

            MyLog.d(TAG, "轮播图基本配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "轮播图配置失败", e);
        }
    }





    /**
     * 加载主题轮播图 - 简化版本
     */
    private void loadThemeBanner() {
        if (themeInfo == null) return;

        MyLog.d(TAG, "加载主题轮播图数据");

        try {
            // 从新的预览图片结构创建轮播图数据
            java.util.List<ThemeBannerModel.BannerItem> bannerItems = new ArrayList<>();

            // 检查是否为默认主题
            if (themeInfo.getClassify() != null && themeInfo.getClassify() == 0) {
                // 默认主题：优先使用previewImages数据，如果没有则使用ThemeBannerModel的默认主题处理逻辑
                if (themeInfo.getPreviewImages() != null && !themeInfo.getPreviewImages().isEmpty()) {
                    MyLog.d(TAG, "默认主题使用previewImages数据");
                    for (UnifiedThemeModel.PreviewImage previewImage : themeInfo.getPreviewImages()) {
                        if (previewImage.getImageUrl() != null && !previewImage.getImageUrl().trim().isEmpty()) {
                            ThemeBannerModel.BannerItem item = new ThemeBannerModel.BannerItem();
                            item.setImageUrl(previewImage.getImageUrl());
                            item.setTitle(previewImage.getTitle() != null ? previewImage.getTitle() : "预览图");
                            bannerItems.add(item);
                        }
                    }
                } else {
                    // 回退到ThemeBannerModel的默认主题处理逻辑
                    MyLog.d(TAG, "默认主题使用ThemeBannerModel处理逻辑");
                    bannerItems = ThemeBannerModel.createFromThemeInfo(themeInfo.getCoverImage());
                }
            } else {
                // 用户主题：使用原有的预览图片逻辑
                // 首先添加封面图片
                if (themeInfo.getCoverImage() != null && !themeInfo.getCoverImage().trim().isEmpty()) {
                    ThemeBannerModel.BannerItem coverItem = new ThemeBannerModel.BannerItem();
                    coverItem.setImageUrl(themeInfo.getCoverImage());
                    coverItem.setTitle("主题封面");
                    bannerItems.add(coverItem);
                }

                // 然后添加预览图片
                if (themeInfo.getPreviewImages() != null && !themeInfo.getPreviewImages().isEmpty()) {
                    for (UnifiedThemeModel.PreviewImage previewImage : themeInfo.getPreviewImages()) {
                        if (previewImage.getImageUrl() != null && !previewImage.getImageUrl().trim().isEmpty()) {
                            ThemeBannerModel.BannerItem item = new ThemeBannerModel.BannerItem();
                            item.setImageUrl(previewImage.getImageUrl());
                            item.setTitle(previewImage.getTitle() != null ? previewImage.getTitle() : "预览图");
                            bannerItems.add(item);
                        }
                    }
                }
            }

            if (bannerItems != null && !bannerItems.isEmpty()) {
                currentBannerItemCount = bannerItems.size();

                // 先设置动态配置
                setupBannerBasicConfig(currentBannerItemCount);

                // 使用create(list)方法创建轮播图
                binding.bannerViewPager.create(bannerItems);

                MyLog.d(TAG, "轮播图数据加载完成，共" + bannerItems.size() + "张图片");

                // 添加调试信息
                for (int i = 0; i < bannerItems.size(); i++) {
                    ThemeBannerModel.BannerItem item = bannerItems.get(i);
                    MyLog.d(TAG, "轮播图项 " + i + ": " + item.getTitle() + " - " + item.getImageUrl());
                }

                // 如果是多张图片，直接启动自动播放
                if (currentBannerItemCount > 1) {
                    binding.bannerViewPager.startLoop();
                    MyLog.d(TAG, "自动轮播已启动");
                }
            } else {
                MyLog.w(TAG, "轮播图数据为空，使用默认数据");
                loadDefaultBannerData();
            }
        } catch (Exception e) {
            MyLog.e(TAG, "加载轮播图数据失败", e);
            loadDefaultBannerData();
        }
    }

    /**
     * 加载默认轮播图数据
     */
    private void loadDefaultBannerData() {
        java.util.List<ThemeBannerModel.BannerItem> defaultItems = new java.util.ArrayList<>();
        defaultItems.add(new ThemeBannerModel.BannerItem(
            "",
            "主题预览",
            "查看主题整体效果",
            0
        ));

        currentBannerItemCount = defaultItems.size();

        // 设置动态配置
        setupBannerBasicConfig(currentBannerItemCount);

        // 创建轮播图
        binding.bannerViewPager.create(defaultItems);

        MyLog.d(TAG, "默认轮播图数据加载完成");
    }

    /**
     * 轮播图点击事件处理 - 简化版本
     */
    private void onBannerImageClick(int position) {
        MyLog.d(TAG, "轮播图点击事件，位置: " + position);

        // 简单的点击反馈
        if (currentBannerItemCount > 1) {
            String hint = "第" + (position + 1) + "张图片，共" + currentBannerItemCount + "张";
            if (position == 0) {
                hint += " - 卡片模式";
            } else if (position == 1) {
                hint += " - 地图模式";
            }
            MToast.makeTextShort(hint);
        } else {
            MToast.makeTextShort("主题预览图");
        }
    }



    /**
     * 显示成功消息
     */
    private void showSuccessMessage(String message) {
        MToast.makeTextShort(message);

        // 添加成功反馈动画
        if (binding != null) {
            // 可以添加一些成功的视觉反馈，比如轻微的震动或者颜色变化
            MyLog.d(TAG, "操作成功: " + message);
        }
    }

    /**
     * 更新按钮状态
     */
    private void updateButtonStates() {
        if (actionBar == null) return;

        // 根据操作状态禁用/启用按钮
        actionBar.setPrimaryActionEnabled(!isOperationInProgress && !isThemeCurrentlyInUse());
        actionBar.setSecondaryActionEnabled(!isOperationInProgress && !isDownloading);

        // 更新按钮文本和进度显示
        if (isOperationInProgress) {
            if (isDownloading) {
                // 显示下载进度
                actionBar.showProgress(0, "准备下载...");
            }
        } else {
            // 恢复正常状态
            actionBar.hideProgress();
            updateActionBar();
        }
    }

    /**
     * 更新统一操作栏 - 支持主题更新功能
     */
    private void updateActionBar() {
        if (themeInfo == null || actionBar == null) return;

        // 正确判断主题是否正在使用
        boolean isCurrentlyInUse = isThemeCurrentlyInUse();

        // 根据主题状态显示不同的按钮
        if (themeInfo.getClassify() != null && themeInfo.getClassify() == 0) {
            // 默认主题，只能应用，不能删除
            String buttonText = isCurrentlyInUse ? "当前使用" : "应用主题";
            actionBar.setPrimaryAction(buttonText, isCurrentlyInUse ? () -> {
                MToast.makeTextShort("该主题已经在使用中");
            } : this::onApplyClick);
            actionBar.setPrimaryActionEnabled(!isCurrentlyInUse);
            actionBar.hideSecondaryAction();

        } else {
            // 用户下载的主题，可以应用、更新和删除
            String primaryButtonText;
            if (isCurrentlyInUse) {
                primaryButtonText = "当前使用";
            } else if (hasUpdate()) {
                primaryButtonText = "更新主题";
            } else {
                primaryButtonText = "应用主题";
            }

            actionBar.setPrimaryAction(primaryButtonText, () -> {
                if (hasUpdate() && !isCurrentlyInUse) {
                    onUpdateClick();
                } else if (!isCurrentlyInUse) {
                    onApplyClick();
                } else {
                    MToast.makeTextShort("该主题已经在使用中");
                }
            });
            actionBar.setPrimaryActionEnabled(!isOperationInProgress);

            // 添加删除按钮
            actionBar.setSecondaryAction("删除主题", this::onDeleteClick);
            actionBar.setSecondaryActionEnabled(!isOperationInProgress);
        }
    }


    /**
     * 应用按钮点击事件
     */
    private void onApplyClick() {
        if (!checkClickDebounce()) return;

        MyLog.d(TAG, "应用主题: " + themeInfo.getThemeName());

        // 显示确认对话框
        showApplyConfirmDialog();
    }





    /**
     * 更新按钮点击事件
     */
    private void onUpdateClick() {
        if (!checkClickDebounce() || isDownloading || isOperationInProgress) return;

        MyLog.d(TAG, "更新主题: " + themeInfo.getThemeName());

        // 显示更新确认对话框
        showUpdateConfirmDialog();
    }

    /**
     * 删除按钮点击事件
     */
    private void onDeleteClick() {
        if (!checkClickDebounce() || isOperationInProgress) return;

        MyLog.d(TAG, "删除主题: " + themeInfo.getThemeName());

        // 显示删除确认对话框
        showDeleteConfirmDialog();
    }

    /**
     * 正确判断主题是否正在使用
     * 通过比较当前设置的主题名称来判断，而不是依赖数据库中的isUse字段
     */
    private boolean isThemeCurrentlyInUse() {
        if (themeInfo == null) return false;

        // 获取当前设置的主题
        String currentDayTheme = SettingsManager.getDayTheme();
        String currentNightTheme = SettingsManager.getNightTheme();

        // 构建主题文件名
        String themeFileName = themeInfo.getThemeName() + ".skin";

        MyLog.d(TAG, "判断主题使用状态:");
        MyLog.d(TAG, "  - 主题名称: " + themeInfo.getThemeName());
        MyLog.d(TAG, "  - 主题类型: " + themeInfo.getThemeType() + " (0=夜间, 1=白天)");
        MyLog.d(TAG, "  - 主题分类: " + themeInfo.getClassify() + " (0=默认主题, 1=用户主题)");
        MyLog.d(TAG, "  - 当前白天主题: " + currentDayTheme);
        MyLog.d(TAG, "  - 当前夜间主题: " + currentNightTheme);
        MyLog.d(TAG, "  - 主题文件名: " + themeFileName);

        boolean isInUse = false;

        // 对于默认主题的特殊处理 - 使用classify字段标识默认主题
        if (themeInfo.getClassify() != null && themeInfo.getClassify() == 0) {
            if (themeInfo.getThemeType() == 0) {
                // 默认白天主题：检查白天主题设置是否为空
                isInUse = currentDayTheme.isEmpty() ||
                         currentDayTheme.contains("默认白天主题");
                MyLog.d(TAG, "  - 默认白天主题判断结果: " + isInUse);
            } else {
                // 默认夜间主题：检查夜间主题设置是否为空或为默认值
                isInUse = currentNightTheme.isEmpty() ||
                         currentNightTheme.equals(SettingsConstants.DEFAULT_NIGHT_THEME) ||
                         currentNightTheme.contains("默认夜间主题");
                MyLog.d(TAG, "  - 默认夜间主题判断结果: " + isInUse);
            }
        } else {
            // 用户下载的主题：检查主题文件名是否匹配
            if (themeInfo.getThemeType() == 0) {
                // 夜间主题
                isInUse = currentNightTheme.equals(themeFileName) ||
                         currentNightTheme.contains(themeInfo.getThemeName());
                MyLog.d(TAG, "  - 用户夜间主题判断结果: " + isInUse);
            } else {
                // 白天主题
                isInUse = currentDayTheme.equals(themeFileName) ||
                         currentDayTheme.contains(themeInfo.getThemeName());
                MyLog.d(TAG, "  - 用户白天主题判断结果: " + isInUse);
            }
        }

        MyLog.d(TAG, "  - 最终判断结果: " + isInUse);
        return isInUse;
    }

    /**
     * 显示删除确认对话框
     */
    private void showDeleteConfirmDialog() {
        if (themeInfo.getClassify() != null && themeInfo.getClassify() == 0) {
            MToast.makeTextShort("默认主题无法删除");
            return;
        }

        String title = "删除主题";
        String message = "确定要删除主题「" + themeInfo.getThemeName() + "」吗？\n删除后将无法恢复。";

        if (isThemeCurrentlyInUse()) {
            message += "\n\n注意：删除当前使用的主题后，系统将自动切换到默认主题。";
        }

        // TODO: 实现删除确认对话框
        // 这里应该显示一个确认对话框，用户确认后调用deleteTheme()
        MyLog.d(TAG, "显示删除确认对话框: " + title + " - " + message);

        // 临时直接调用删除方法，实际应该在用户确认后调用
        deleteTheme();
    }

    /**
     * 检查点击防抖
     */
    private boolean checkClickDebounce() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_TIME) {
            MyLog.d(TAG, "点击过于频繁，忽略本次点击");
            return false;
        }
        lastClickTime = currentTime;
        return true;
    }



    /**
     * 显示应用确认对话框
     */
    private void showApplyConfirmDialog() {
        if (isThemeCurrentlyInUse()) {
            MToast.makeTextShort("该主题已经在使用中");
            return;
        }

        String message = "确定要应用主题「" + themeInfo.getThemeName() + "」吗？";

        // 检查是否需要切换模式
        int themeType = themeInfo.getThemeType();
        boolean isNightMode = SkinManager.getInstance().isNightModeNow();

        if ((themeType == 0 && !isNightMode) || (themeType == 1 && isNightMode)) {
            String modeText = themeType == 0 ? "夜间模式" : "白天模式";
            message += "\n\n应用后将自动切换到" + modeText;
        }

        // TODO: 使用项目统一的确认对话框
        // 这里暂时直接执行，实际项目中应该显示确认对话框
        MyLog.d(TAG, "确认应用主题: " + message);
        applyTheme();
    }

    /**
     * 应用主题
     */
    private void applyTheme() {
        if (isOperationInProgress) return;

        isOperationInProgress = true;
        updateButtonStates();

        try {
            int themeType = themeInfo.getThemeType();
            boolean isNightMode = SkinManager.getInstance().isNightModeNow();

            if (themeType == 0) {
                // 夜间主题
                registerAndLoadSkin();

                if (themeInfo.getClassify() == 0) {
                    // 默认夜间主题：设置为默认夜间主题
                    SettingsManager.setNightTheme(SettingsConstants.DEFAULT_NIGHT_THEME);
                } else {
                    // 用户夜间主题：设置为主题文件名
                    SettingsManager.setNightTheme(themeInfo.getThemePackage().getFileName());
                }

                if (!isNightMode) {
                    SettingsManager.setThemeMode(2); // 切换到夜间模式
                }
                updateThemeUsageStatus();
                showSuccessMessage("已应用夜间主题: " + themeInfo.getThemeName());

            } else if (themeType == 1) {
                // 白天主题
                registerAndLoadSkin();

                if (themeInfo.getClassify() == 0) {
                    // 默认白天主题：设置为空（表示使用默认）
                    SettingsManager.setDayTheme("");
                } else {
                    // 用户白天主题：设置为主题文件名
                    SettingsManager.setDayTheme(themeInfo.getThemeName() + ".skin");
                }

                if (isNightMode) {
                    SettingsManager.setThemeMode(1); // 切换到白天模式
                }
                updateThemeUsageStatus();
                showSuccessMessage("已应用白天主题: " + themeInfo.getThemeName());
            }
        } catch (Exception e) {
            MyLog.e(TAG, "应用主题失败", e);
            MToast.makeTextShort("应用主题失败，请重试");
        } finally {
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    /**
     * 注册并加载皮肤 - 修复默认主题处理
     */
    private void registerAndLoadSkin() {
        if (themeInfo.getClassify() == 0) {
            // 默认主题处理
            if (themeInfo.getThemeType() == 0) {
                // 默认夜间主题：加载默认夜间皮肤
                MyLog.d(TAG, "应用默认夜间主题");
                SkinManager.getInstance().loadSkin(SettingsConstants.DEFAULT_NIGHT_THEME);
            } else {
                // 默认白天主题：恢复默认白天主题
                MyLog.d(TAG, "应用默认白天主题");
                SkinManager.getInstance().restoreDefaultTheme();
            }
        } else {
            // 用户主题，加载皮肤文件
            String skinPath = themeInfo.getThemePackage().getFileName();
            if (skinPath != null && !skinPath.isEmpty()) {
                // 如果是网络URL，使用本地路径
                if (skinPath.startsWith("http")) {
                    String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
                    skinPath = absolutePath + "/skin/" + themeInfo.getThemeName() + ".skin";
                }
                SkinManager.getInstance().registerFileSkin(skinPath);
                SkinManager.getInstance().loadSkin(themeInfo.getThemeName() + ".skin");
            }
        }
        MyLog.v(TAG, "应用主题: " + themeInfo.getThemeName() + ", 类型: " + (themeInfo.getThemeType() == 0 ? "夜间" : "白天"));
    }

    /**
     * 更新主题使用状态
     */
    private void updateThemeUsageStatus() {
        // 更新本地状态
        themeInfo.setUse(true);
        themeInfo.setIsCurrentTheme(true);

        // 更新数据库（仅对非默认主题）
        if (themeInfo.getClassify() != null && themeInfo.getClassify() != 0) {
            disposables.add(
                dbManager.updateUsageStatus(themeInfo.getId(), true)
                    .subscribe(
                        result -> {
                            MyLog.v(TAG, "主题使用状态更新成功，影响行数: " + result);
                            updateActionBar();
                        },
                        throwable -> {
                            MyLog.e(TAG, "主题使用状态更新失败", throwable);
                            updateActionBar(); // 即使失败也要更新UI
                        }
                    )
            );
        } else {
            updateActionBar();
        }
    }



    /**
     * 下载主题
     */
    private void downloadTheme() {
        if (isOperationInProgress || !NetworkUtils.isNetworkConnected(requireSceneContext())) {
            if (!NetworkUtils.isNetworkConnected(requireSceneContext())) {
                MToast.makeTextShort("网络连接不可用，请检查网络设置");
            }
            return;
        }

        isOperationInProgress = true;
        isDownloading = true;
        updateActionBar();

        MyLog.d(TAG, "开始下载主题: " + themeInfo.getThemeName());

        // 更新下载统计
        updateDownloadStats();

        // 开始真实下载（主题包信息已经在详情API中获取）
        downloadTimeMillis = String.valueOf(System.currentTimeMillis());
        startRealDownload();
    }

    /**
     * 更新下载统计 - 参考AllThemeFragment的updateDownloads方法
     */
    @SuppressLint("CheckResult")
    private void updateDownloadStats() {
        if (themeInfo == null || (themeInfo.getClassify() != null && themeInfo.getClassify() == 0)) {
            // 默认主题不需要更新下载统计
            return;
        }

        MyLog.d(TAG, "更新下载统计: " + themeInfo.getThemeName());


        String requestBody = "{\n" +
                "  \"packageId\": null,\n" +
                "  \"userId\": null,\n" +
                "  \"deviceId\": \"" + android.provider.Settings.Secure.getString(
                requireSceneContext().getContentResolver(),
                android.provider.Settings.Secure.ANDROID_ID) + "\",\n" +
                "  \"appVersion\": \"" + com.smartcar.easylauncher.BuildConfig.VERSION_NAME + "\",\n" +
                "  \"platform\": \"android\",\n" +
                "  \"downloadSource\": \"theme_detail\"\n" +
                "}";

        disposables.add(
                RxHttp.postJson(Const.NEW_THEME_DOWNLOAD + themeInfo.getId())
                        .addAll(requestBody)
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                responseString -> {
                                    try {
                                        Gson gson = new Gson();
                                        TypeToken<ApiResponse<Object>> typeToken = new TypeToken<ApiResponse<Object>>() {
                                        };
                                        ApiResponse<Object> response = gson.fromJson(responseString, typeToken.getType());

                                        if (response != null && response.isSuccess()) {
                                            MyLog.v(TAG, "新API下载统计更新成功: " + response.getMsg());
                                            // 更新本地显示的下载量
                                            Long currentCount = themeInfo.getDownloadCount() != null ? themeInfo.getDownloadCount() : 0L;
                                            themeInfo.setDownloadCount(currentCount + 1);
                                        } else {
                                            MyLog.w(TAG, "新API下载统计响应异常: " + (response != null ? response.getMsg() : "null"));
                                        }
                                    } catch (Exception e) {
                                        MyLog.e(TAG, "解析下载统计响应失败", e);
                                    }
                                },
                                throwable -> {
                                    MyLog.e(TAG, "新API下载统计更新失败", throwable);
                                    // 静默失败，不影响下载流程
                                }
                        )
        );
    }

    /**
     * 开始真实下载 - 参考ThemeDetailScene
     */
    private void startRealDownload() {
        if (themeInfo.getThemePackage() == null) {
            onDownloadError(new RuntimeException("主题包信息不存在"));
            return;
        }
        
        String fileName = themeInfo.getThemePackage().getFileName();
        String downloadUrl = getDownloadUrl();

        if (fileName == null || downloadUrl == null) {
            onDownloadError(new RuntimeException("无法获取主题文件信息"));
            return;
        }

        // 文件存储路径
        String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
        String destPath = absolutePath + "/skin/" + fileName;

        MyLog.d(TAG, "开始下载主题文件");
        MyLog.d(TAG, "  - 文件名: " + fileName);
        MyLog.d(TAG, "  - 下载URL: " + downloadUrl);
        MyLog.d(TAG, "  - 存储路径: " + destPath);

        downloadDisposable = RxHttp.get(downloadUrl)
                .tag(downloadTimeMillis)
                .toDownloadObservable(destPath)
                .onMainProgress(progress -> {
                    // 下载进度回调
                    int currentProgress = progress.getProgress(); // 当前进度 0-100
                    long currentSize = progress.getCurrentSize(); // 当前已下载的字节大小
                    long totalSize = progress.getTotalSize(); // 要下载的总字节大小

                    MyLog.v(TAG, "下载进度: " + currentProgress + "%");

                    // 更新进度显示
                    if (actionBar != null) {
                        actionBar.showProgress(currentProgress, "下载中...");
                    }
                })
                .subscribe(
                    downloadPath -> {
                        // 下载完成
                        MyLog.d(TAG, "下载完成: " + downloadPath);
                        onRealDownloadComplete(downloadPath);
                    },
                    throwable -> {
                        // 下载失败
                        MyLog.e(TAG, "下载失败", throwable);
                        onDownloadError(throwable);
                    }
                );

        // 添加到disposables中管理
        disposables.add(downloadDisposable);
    }

    /**
     * 获取下载URL - 处理相对路径
     */
    private String getDownloadUrl() {
        if (themeInfo.getThemePackage() == null) {
            return null;
        }
        
        String downloadUrl = themeInfo.getThemePackage().getDownloadUrl();
        if (downloadUrl == null || downloadUrl.trim().isEmpty()) {
            // 如果没有downloadUrl，尝试使用packagePath
            downloadUrl = themeInfo.getThemePackage().getPackagePath();
        }
        
        if (downloadUrl == null || downloadUrl.trim().isEmpty()) {
            MyLog.e(TAG, "主题包下载URL为空");
            return null;
        }
        
        // 如果是相对路径，添加基础URL
        if (!downloadUrl.startsWith("http")) {
            downloadUrl = Const.DEV_BASEURL + downloadUrl;
            MyLog.d(TAG, "转换相对路径URL: " + downloadUrl);
        }
        
        return downloadUrl;
    }


    /**
     * 真实下载完成处理 - 包含更新逻辑
     */
    private void onRealDownloadComplete(String downloadPath) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        // 记录是否为更新操作
        boolean isUpdate = latestServerTheme != null;

        // 更新主题下载状态
        themeInfo.setIsDownloaded(true);
        themeInfo.setLocalFilePath(downloadPath);
        themeInfo.setDownloadTime(String.valueOf(System.currentTimeMillis()));

        // 确保所有必需字段都有值，避免数据库插入时的NullPointerException
        ensureRequiredFields(themeInfo);

        MyLog.d(TAG, "主题下载完成，准备保存到数据库: " + themeInfo.getThemeName() + 
            " (更新操作: " + isUpdate + ")");

        // 保存主题到数据库 - 如果是更新，会覆盖旧版本
        disposables.add(
                dbManager.saveDownloadedTheme(themeInfo)
                        .subscribe(
                                result -> {
                                    MyLog.v(TAG, "主题保存到数据库成功，ID: " + result);
                                    
                                    // 显示成功消息
                                    String message = isUpdate ? "主题更新完成" : "主题下载完成，可在「我的主题」中查看";
                                    showSuccessMessage(message);
                                    
                                    // 重置更新状态（因为已经是最新版本了）
                                    latestServerTheme = null;
                                    themeInfo.setHasUpdate(false);
                                    
                                    // 刷新UI
                                    updateActionBar();
                                    showUpdateInfo(); // 刷新版本信息显示
                                },
                                throwable -> {
                                    MyLog.e(TAG, "简化保存方法失败，尝试基本保存方法", throwable);
                                    // 尝试备用的基本保存方法
                                    tryBasicSave(themeInfo);
                                }
                        )
        );
    }

    /**
     * 确保所有必需字段都有值
     */
    private void ensureRequiredFields(UnifiedThemeModel theme) {
        // 确保ID不为空
        if (theme.getId() == null) {
            MyLog.e(TAG, "主题ID为空，无法保存");
            return;
        }

        // 确保主题名称不为空
        if (theme.getThemeName() == null || theme.getThemeName().trim().isEmpty()) {
            theme.setThemeName("主题_" + theme.getId());
            MyLog.d(TAG, "设置默认主题名称: " + theme.getThemeName());
        }

        // 确保作者不为空
        if (theme.getAuthor() == null || theme.getAuthor().trim().isEmpty()) {
            theme.setAuthor("未知作者");
            MyLog.d(TAG, "设置默认作者: " + theme.getAuthor());
        }

        // 确保主题类型不为空
        if (theme.getThemeType() == null) {
            theme.setThemeType(0); // 默认为夜间主题
            MyLog.d(TAG, "设置默认主题类型: " + theme.getThemeType());
        }

        // 确保下载状态不为空
        if (theme.getIsDownloaded() == null) {
            theme.setIsDownloaded(true);
            MyLog.d(TAG, "设置下载状态: " + theme.getIsDownloaded());
        }

        // 确保当前主题状态不为空
        if (theme.getIsCurrentTheme() == null) {
            theme.setIsCurrentTheme(false);
            MyLog.d(TAG, "设置当前主题状态: " + theme.getIsCurrentTheme());
        }

        // 确保分类不为空（数据库中被标记为NOT NULL）
        if (theme.getClassify() == null) {
            theme.setClassify(1); // 默认为用户主题
            MyLog.d(TAG, "设置默认分类: " + theme.getClassify());
        }

        // 确保使用状态不为空（数据库中被标记为NOT NULL）
        if (theme.isUse() == null) {
            theme.setUse(false);
            MyLog.d(TAG, "设置使用状态: " + theme.isUse());
        }

        // 确保下载时间不为空（如果已下载）
        if (theme.getIsDownloaded() != null && theme.getIsDownloaded() && 
            (theme.getDownloadTime() == null || theme.getDownloadTime().trim().isEmpty())) {
            theme.setDownloadTime(String.valueOf(System.currentTimeMillis()));
            MyLog.d(TAG, "设置下载时间: " + theme.getDownloadTime());
        }

        // 确保本地文件路径不为空（如果已下载）
        if (theme.getIsDownloaded() != null && theme.getIsDownloaded() && 
            (theme.getLocalFilePath() == null || theme.getLocalFilePath().trim().isEmpty())) {
            // 如果没有本地文件路径，尝试从主题包信息构建
            if (theme.getThemePackage() != null && theme.getThemePackage().getFileName() != null) {
                String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
                String localPath = absolutePath + "/skin/" + theme.getThemePackage().getFileName();
                theme.setLocalFilePath(localPath);
                MyLog.d(TAG, "设置本地文件路径: " + theme.getLocalFilePath());
            }
        }

        // 确保updateTime不为空（数据库操作时可能被当作非空字段）
        if (theme.getUpdateTime() == null || theme.getUpdateTime().trim().isEmpty()) {
            theme.setUpdateTime(String.valueOf(System.currentTimeMillis()));
            MyLog.d(TAG, "设置更新时间: " + theme.getUpdateTime());
        }

        // 确保createTime不为空（如果为空）
        if (theme.getCreateTime() == null || theme.getCreateTime().trim().isEmpty()) {
            theme.setCreateTime(String.valueOf(System.currentTimeMillis()));
            MyLog.d(TAG, "设置创建时间: " + theme.getCreateTime());
        }

        MyLog.d(TAG, "必需字段检查完成");
    }

    /**
     * 尝试基本保存方法 - 备用方案
     */
    private void tryBasicSave(UnifiedThemeModel themeInfo) {
        MyLog.d(TAG, "尝试使用基本保存方法");

        // 确保所有必需字段都有值
        ensureRequiredFields(themeInfo);

        disposables.add(
                dbManager.saveThemeBasic(themeInfo)
                        .subscribe(
                                result -> {
                                    MyLog.v(TAG, "基本保存方法成功，ID: " + result);
                                    MToast.makeTextShort("主题下载完成");
                                    updateActionBar();
                                },
                                throwable -> {
                                    MyLog.e(TAG, "基本保存方法也失败", throwable);
                                    MToast.makeTextShort("主题下载完成，但保存失败");
                                    updateActionBar();
                                }
                        )
        );
    }

    /**
     * 下载错误处理
     */
    private void onDownloadError(Throwable throwable) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        String errorMessage = "下载失败";
        if (throwable != null) {
            errorMessage += ": " + throwable.getMessage();
        }

        MyLog.e(TAG, errorMessage, throwable);
        showErrorMessage(errorMessage);

        // 更新操作栏状态
        updateActionBar();
    }


    /**
     * 删除主题
     */
    private void deleteTheme() {
        if (themeInfo.getClassify() == 0) {
            MToast.makeTextShort("默认主题无法删除");
            return;
        }

        // 删除主题文件
        boolean fileDeleted = FileUtils.deleteFolder(themeInfo.getLocalFilePath());
        MyLog.v(TAG, "主题文件删除结果: " + fileDeleted);

        // 从数据库删除
        disposables.add(
            dbManager.deleteTheme(themeInfo.getId())
                .subscribe(
                    result -> {
                        MyLog.v(TAG, "主题删除成功，影响行数: " + result);

                        // 如果删除的是当前使用的主题，重置为默认主题
                        if (isThemeCurrentlyInUse()) {
                            if (themeInfo.getThemeType() == 0) {
                                SettingsManager.setNightTheme("");
                            } else {
                                SettingsManager.setDayTheme("");
                            }
                        }

                        MToast.makeTextShort("主题删除成功");
                        getNavigationScene(this).pop(); // 返回上一页
                    },
                    throwable -> {
                        MyLog.e(TAG, "主题删除失败", throwable);
                        MToast.makeTextShort("主题删除失败: " + throwable.getMessage());
                    }
                )
        );
    }





    /**
     * 显示错误消息
     */
    private void showErrorMessage(String message) {
        MToast.makeTextShort(message);
        MyLog.e(TAG, "错误: " + message);
    }


    /**
     * 检查主题更新 - 使用新API获取最新版本信息
     */
    @SuppressLint("CheckResult")
    private void checkThemeUpdate() {
        if (isCheckingUpdate || themeInfo == null ||
            (themeInfo.getClassify() != null && themeInfo.getClassify() == 0) ||
            themeInfo.getId() == null) {
            return;
        }

        isCheckingUpdate = true;
        MyLog.v(TAG, "检查主题更新: " + themeInfo.getThemeName() + " (ID: " + themeInfo.getId() + ")");

        // 使用新API检查特定主题的详情
        disposables.add(
                RxHttp.get(Const.NEW_THEME_DETAIL + themeInfo.getId())
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleThemeUpdateCheckSuccess,
                                this::handleThemeUpdateCheckError
                        )
        );
    }

    /**
     * 处理主题更新检查成功
     */
    private void handleThemeUpdateCheckSuccess(String responseString) {
        isCheckingUpdate = false;

        MyLog.v(TAG, "主题详情API响应: " + responseString);

        try {
            Gson gson = new Gson();
            TypeToken<ApiResponse<UnifiedThemeModel>> typeToken = new TypeToken<ApiResponse<UnifiedThemeModel>>(){};
            ApiResponse<UnifiedThemeModel> response = gson.fromJson(responseString, typeToken.getType());

            if (response != null && response.isSuccess() && response.getData() != null) {
                UnifiedThemeModel serverThemeInfo = response.getData();
                MyLog.v(TAG, "主题详情解析成功: " + serverThemeInfo.getThemeName());

                // 检查是否有可用的主题包并比较版本
                if (serverThemeInfo.getThemePackage() != null) {
                    boolean needUpdate = checkIfNeedUpdate(themeInfo, serverThemeInfo);
                    
                    if (needUpdate) {
                        // 保存最新的服务器主题信息
                        latestServerTheme = serverThemeInfo;
                        
                        // 设置更新标记
                        themeInfo.setHasUpdate(true);
                        
                        MyLog.d(TAG, "发现主题更新: " + themeInfo.getThemeName());
                        
                        // 更新本地主题信息但不覆盖下载状态
                        updateLocalThemeInfo(serverThemeInfo);
                        
                        // 刷新UI显示
                        updateThemeInfo();
                        updateActionBar();
                        showUpdateInfo();
                        
                        // 保存更新状态到数据库
                        saveUpdateStatusToDatabase();
                    } else {
                        // 确保hasUpdate为false
                        themeInfo.setHasUpdate(false);
                        MyLog.d(TAG, "主题已是最新版本: " + themeInfo.getThemeName());
                    }
                } else {
                    MyLog.w(TAG, "服务器主题详情中没有主题包信息");
                }
            } else {
                MyLog.w(TAG, "主题详情API响应异常: " + (response != null ? response.getMsg() : "null"));
            }
        } catch (Exception e) {
            MyLog.e(TAG, "解析主题详情响应失败", e);
        }
    }

    /**
     * 保存更新状态到数据库
     */
    private void saveUpdateStatusToDatabase() {
        disposables.add(
            dbManager.update(themeInfo)
                .subscribe(
                    result -> {
                        MyLog.v(TAG, "主题更新状态保存成功: " + themeInfo.getThemeName());
                    },
                    throwable -> {
                        MyLog.e(TAG, "主题更新状态保存失败: " + themeInfo.getThemeName(), throwable);
                    }
                )
        );
    }

    /**
     * 检查是否需要更新 - 比较本地和服务器版本
     */
    private boolean checkIfNeedUpdate(UnifiedThemeModel localTheme, UnifiedThemeModel serverTheme) {
        UnifiedThemeModel.ThemePackage localPackage = localTheme.getThemePackage();
        UnifiedThemeModel.ThemePackage serverPackage = serverTheme.getThemePackage();
        
        if (localPackage == null || serverPackage == null) {
            MyLog.d(TAG, "无法比较版本：本地包=" + (localPackage != null) + ", 服务器包=" + (serverPackage != null));
            return false;
        }
        
        Integer localVersionCode = localPackage.getVersionCode();
        Integer serverVersionCode = serverPackage.getVersionCode();
        
        // 设置默认版本号
        if (localVersionCode == null) {
            localVersionCode = 1;
        }
        if (serverVersionCode == null) {
            serverVersionCode = 1;
        }
        
        boolean needUpdate = serverVersionCode > localVersionCode;
        
        MyLog.d(TAG, String.format("版本比较: %s - 本地v%d vs 服务器v%d, 需要更新: %s",
                localTheme.getThemeName(), localVersionCode, serverVersionCode, needUpdate));
        
        return needUpdate;
    }

    /**
     * 更新本地主题信息 - 不覆盖下载状态
     */
    private void updateLocalThemeInfo(UnifiedThemeModel serverTheme) {
        // 更新主题包信息
        if (serverTheme.getThemePackage() != null) {
            themeInfo.setThemePackage(serverTheme.getThemePackage());
        }
        
        // 更新下载统计信息
        if (serverTheme.getDownloadCount() != null) {
            themeInfo.setDownloadCount(serverTheme.getDownloadCount());
        }
        
        // 更新其他信息（但保留本地的下载状态和文件路径）
        if (serverTheme.getThemeDescription() != null && !serverTheme.getThemeDescription().trim().isEmpty()) {
            themeInfo.setThemeDescription(serverTheme.getThemeDescription());
        }
        
        if (serverTheme.getLabel() != null && !serverTheme.getLabel().trim().isEmpty()) {
            themeInfo.setLabel(serverTheme.getLabel());
        }
        
        // 更新预览图片
        if (serverTheme.getPreviewImages() != null && !serverTheme.getPreviewImages().isEmpty()) {
            themeInfo.setPreviewImages(serverTheme.getPreviewImages());
        }
        
        MyLog.d(TAG, "本地主题信息已更新: " + themeInfo.getThemeName());
    }

    /**
     * 处理主题更新检查失败
     */
    private void handleThemeUpdateCheckError(Throwable throwable) {
        isCheckingUpdate = false;
        MyLog.e(TAG, "主题更新检查失败", throwable);
        // 静默失败，不影响用户体验
    }

    /**
     * 检查是否有更新 - 基于主题的hasUpdate字段
     */
    private boolean hasUpdate() {
        return themeInfo != null && Boolean.TRUE.equals(themeInfo.getHasUpdate());
    }

    /**
     * 显示更新确认对话框
     */
    private void showUpdateConfirmDialog() {
        if (latestServerTheme == null || latestServerTheme.getThemePackage() == null) {
            MToast.makeTextShort("获取更新信息失败，请重试");
            return;
        }

        String versionName = latestServerTheme.getThemePackage().getVersionName();
        String title = "更新主题";
        String message = "发现新版本 v" + versionName + "，确定要更新主题「" + themeInfo.getThemeName() + "」吗？";

        String updateDescription = latestServerTheme.getThemePackage().getUpdateDescription();
        if (updateDescription != null && !updateDescription.trim().isEmpty()) {
            message += "\n\n更新内容：\n" + updateDescription;
        }

        // TODO: 使用项目统一的确认对话框
        // 这里暂时直接执行，实际项目中应该显示确认对话框
        MyLog.d(TAG, "确认更新主题: " + message);
        updateTheme();
    }

    /**
     * 更新主题 - 重新下载最新版本
     */
    private void updateTheme() {
        if (latestServerTheme == null || latestServerTheme.getThemePackage() == null) {
            MToast.makeTextShort("更新信息不完整，请重试");
            return;
        }

        MyLog.d(TAG, "开始更新主题: " + themeInfo.getThemeName());

        // 更新主题信息为最新版本（关键：更新下载URL等信息）
        themeInfo.setThemePackage(latestServerTheme.getThemePackage());
        
        // 重置下载状态，准备重新下载
        themeInfo.setIsDownloaded(false);
        themeInfo.setLocalFilePath(null);
        
        // 执行下载（复用现有的下载逻辑）
        downloadTheme();
    }



    @Override
    public void onResume() {
        super.onResume();

        // 恢复轮播图自动播放
        if (currentBannerItemCount > 1) {
            binding.bannerViewPager.startLoop();
            MyLog.d(TAG, "页面恢复，自动轮播已启动");
        }

        // 重置操作状态（防止异常情况下状态未正确恢复）
        if (isOperationInProgress && !isDownloading) {
            MyLog.w(TAG, "检测到异常操作状态，重置状态");
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        // 暂停轮播图自动播放
        binding.bannerViewPager.stopLoop();
        MyLog.d(TAG, "页面暂停，自动轮播已停止");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 取消下载
        if (downloadDisposable != null && !downloadDisposable.isDisposed()) {
            downloadDisposable.dispose();
        }

        // 停止轮播图
        binding.bannerViewPager.stopLoop();

        // 清理资源
        disposables.clear();
        bannerAdapter = null;
        actionBar = null;
        binding = null;
    }

    /**
     * 更新主题信息显示
     */
    private void updateThemeInfo() {
        if (themeInfo == null) return;

        // 重新设置主题基本信息
        binding.tvThemeName.setText(themeInfo.getThemeName());
        binding.tvThemeAuthor.setText("作者: " + themeInfo.getAuthor());
        binding.tvThemeDescription.setText(themeInfo.getThemeDescription());

        // 使用themeTypeName，如果为空则根据themeType判断
        String themeType = themeInfo.getThemeTypeName();
        if (themeType == null || themeType.isEmpty()) {
            themeType = themeInfo.getThemeType() == 0 ? "白天主题" : "夜间主题";
        }
        binding.tvThemeType.setText(themeType);
        binding.tvDownloadCount.setText("下载量: " + (themeInfo.getDownloadCount() != null ? themeInfo.getDownloadCount() : 0));

        // 设置版本信息（如果有主题包）
        if (themeInfo.getThemePackage() != null) {
            binding.tvThemeVersion.setText("版本: " + themeInfo.getThemePackage().getVersionName());
        } else {
            binding.tvThemeVersion.setText("版本: 未知");
        }

        // 设置主题标签
        if (themeInfo.getLabel() != null && !themeInfo.getLabel().trim().isEmpty()) {
            binding.tvThemeLabel.setText(themeInfo.getLabel());
            binding.tvThemeLabel.setVisibility(View.VISIBLE);
        } else {
            binding.tvThemeLabel.setVisibility(View.GONE);
        }
    }

    /**
     * 更新主题的更新状态到数据库
     */
    private void updateThemeUpdateStatus(boolean needUpdate) {
        if (dbManager != null && themeInfo != null && themeInfo.getId() != null) {
            // 注意：UnifiedThemeModel 可能没有 update 字段，这个方法可能需要调整
            disposables.add(
                    dbManager.updateThemeUpdateStatus(themeInfo.getId(), needUpdate)
                            .subscribe(
                                    result -> MyLog.d(TAG, "主题更新状态已保存到数据库，影响行数: " + result),
                                    throwable -> MyLog.e(TAG, "保存主题更新状态失败", throwable)
                            )
            );
        }
    }
}
