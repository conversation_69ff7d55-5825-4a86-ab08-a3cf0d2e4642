package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.bytedance.scene.animation.SharedElementSceneTransitionExecutor;
import com.bytedance.scene.animation.interaction.scenetransition.AutoSceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.SceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.visiblity.Fade;
import com.bytedance.scene.interfaces.PushOptions;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.layoutmanager.QuickGridLayoutManager;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.adapter.theme.SceneLocalThemeAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneLocalThemeBinding;
import com.smartcar.easylauncher.data.database.dbmager.ThemeDbManager;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.theme.ThemeListModel;
import com.smartcar.easylauncher.data.model.theme.api.TableDataInfo;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.shared.utils.ui.ScreenUtils;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.data.model.theme.api.ApiResponse;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import rxhttp.RxHttp;


/**
 * 本地主题Scene - 优化版本
 * 展示用户已下载的本地主题，支持主题应用、删除和管理
 * 优化内容：
 * 1. 参考FindThemeScene的优化架构
 * 2. 使用BaseQuickAdapter4的空视图功能
 * 3. 优化命名规范，提高代码可读性
 * 4. 添加默认主题图片资源支持
 * 5. 减少布局层级，提升性能
 * 6. 添加版本更新检查功能，参考AllThemeFragment的逻辑
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class LocalThemeScene extends BaseScene {

    private static final String TAG = "LocalThemeScene";

    private SceneLocalThemeBinding mBinding;
    private SceneLocalThemeAdapter mThemeAdapter;
    private final List<UnifiedThemeModel> mLocalThemeDataList = new ArrayList<>();
    private final CompositeDisposable mDisposables = new CompositeDisposable();
    private ThemeDbManager mDbManager;

    // 版本更新相关
    private final List<UnifiedThemeModel> mServerThemeList = new ArrayList<>();
    private boolean mIsCheckingUpdate = false;

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = SceneLocalThemeBinding.inflate(inflater, container, false);
        return mBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setupViews();
        initializeData();
        loadLocalThemeData();

        // 检查主题更新（如果网络可用）
        if (NetworkUtils.isNetworkConnected(requireActivity())) {
            checkThemeUpdatesEnhanced();
        }
    }

    /**
     * 设置视图组件 - 优化版本
     */
    private void setupViews() {
        setupRecyclerView();
        setupAdapter();
        setupSwipeRefresh();
        setupBackButton();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        int spanCount = calculateOptimalSpanCount();
        QuickGridLayoutManager layoutManager = new QuickGridLayoutManager(requireActivity(), spanCount);
        mBinding.rvTheme.setLayoutManager(layoutManager);

        // RecyclerView性能优化配置
        mBinding.rvTheme.setHasFixedSize(true);
        mBinding.rvTheme.setItemViewCacheSize(20);
    }

    /**
     * 设置适配器和空视图
     */
    private void setupAdapter() {
        mThemeAdapter = new SceneLocalThemeAdapter();
        mThemeAdapter.setOnItemClickListener(this::handleThemeItemClick);
        mThemeAdapter.setOnItemLongClickListener(this::handleThemeItemLongClick);

        // 初始状态禁用空视图，避免在加载时显示
        mThemeAdapter.setStateViewEnable(false);
        mThemeAdapter.setUseStateViewSize(true);
        View emptyView = LayoutInflater.from(requireActivity()).inflate(R.layout.local_theme_empty_view, mBinding.rvTheme, false);
        mThemeAdapter.setStateView(emptyView);

        mBinding.rvTheme.setAdapter(mThemeAdapter);
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        mBinding.swipeRefreshLayout.setOnRefreshListener(this::refreshLocalThemeData);
        mBinding.swipeRefreshLayout.setColorSchemeResources(
                R.color.brand_primary,
                R.color.main_button_confirm_center_color,
                R.color.main_button_confirm_end_color
        );
    }

    /**
     * 设置返回按钮
     */
    private void setupBackButton() {
        mBinding.btBack.setOnClickListener(v -> {
            MyLog.d(TAG, "点击返回按钮");
            getNavigationScene(this).pop();
        });
    }

    /**
     * 初始化数据管理器
     */
    private void initializeData() {
        mDbManager = ThemeDbManager.getInstance();
    }

    /**
     * 从数据库加载本地主题数据
     */
    @SuppressLint("CheckResult")
    private void loadLocalThemeData() {
        setLoadingState(true);

        MyLog.v(TAG, "开始加载本地主题数据");

        mDisposables.add(
                mDbManager.getDownloadedThemes()
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleLocalThemeDataSuccess,
                                this::handleLocalThemeDataError
                        )
        );
    }

    /**
     * 刷新本地主题数据
     */
    private void refreshLocalThemeData() {
        loadLocalThemeData();

        // 同时检查主题更新
        if (NetworkUtils.isNetworkConnected(requireActivity())) {
            checkThemeUpdatesEnhanced();
        }
    }

    /**
     * 处理本地主题数据加载成功 - 参考FindThemeScene的推荐逻辑优化
     */
    private void handleLocalThemeDataSuccess(List<UnifiedThemeModel> themes) {
        setLoadingState(false);

        MyLog.v(TAG, "本地主题数据加载成功，数量: " + themes.size());

        mLocalThemeDataList.clear();

        // 添加默认主题
        addDefaultThemes();

        // 处理用户下载的主题 - 参考推荐主题的处理逻辑
        List<UnifiedThemeModel> processedThemes = processAndSortLocalThemes(themes);
        
        // 添加处理后的主题
        for (UnifiedThemeModel theme : processedThemes) {
            // 重新检查用户主题的使用状态
            boolean isCurrentlyUsed = isThemeCurrentlyInUse(theme);
            theme.setIsCurrentTheme(isCurrentlyUsed);
            theme.setUse(isCurrentlyUsed); // 同时设置use字段
            
            // 计算推荐度（参考FindThemeScene的热度排序逻辑）
            calculateThemeRecommendScore(theme);
            
            mLocalThemeDataList.add(theme);
        }

        // 对所有主题进行智能排序（参考推荐主题的排序逻辑）
        sortThemesByRecommendation();

        updateAdapterData();

        // 显示优化后的统计信息
        showThemeStatistics();

        // 检查主题更新状态
        checkAndUpdateThemeVersions();
    }

    /**
     * 处理本地主题数据加载失败
     */
    private void handleLocalThemeDataError(Throwable throwable) {
        setLoadingState(false);
        MyLog.e(TAG, "本地主题数据加载失败", throwable);
        MToast.makeTextShort("本地主题数据加载失败，请检查存储权限");

        // 仍然显示默认主题
        mLocalThemeDataList.clear();
        addDefaultThemes();
        updateAdapterData();
    }

    /**
     * 添加默认主题
     */
    private void addDefaultThemes() {
        // 添加默认白天主题
        UnifiedThemeModel defaultDayTheme = createDefaultTheme(
                -1L, // 使用负数ID标识默认主题
                "默认白天主题",
                1, // 白天主题
                "系统默认的白天主题",
                R.drawable.default_card_daytheme
        );
        mLocalThemeDataList.add(defaultDayTheme);

        // 添加默认夜间主题
        UnifiedThemeModel defaultNightTheme = createDefaultTheme(
                -2L, // 使用负数ID标识默认主题
                "默认夜间主题",
                0, // 夜间主题
                "系统默认的夜间主题",
                R.drawable.default_card_nighttheme
        );
        mLocalThemeDataList.add(defaultNightTheme);
    }

    /**
     * 创建默认主题 - 适配UnifiedThemeModel
     */
    private UnifiedThemeModel createDefaultTheme(Long id, String name, int themeType, String content, int imageResId) {
        UnifiedThemeModel theme = new UnifiedThemeModel();
        theme.setId(id);
        theme.setThemeName(name);
        theme.setThemeType(themeType);
        theme.setThemeDescription(content);
        theme.setAuthor("系统");
        theme.setDownloadCount(0L);
        theme.setHeat(0L);

        // 设置默认主题图片资源ID（用于适配器识别）
        theme.setCoverImage(String.valueOf(imageResId));

        // 为默认主题添加预览图片数据
        List<UnifiedThemeModel.PreviewImage> previewImages = new ArrayList<>();
        UnifiedThemeModel.PreviewImage previewImage = new UnifiedThemeModel.PreviewImage();
        previewImage.setId(id);
        previewImage.setThemeId(id);
        previewImage.setTitle("主题预览");
        previewImage.setDescription("查看主题整体效果");
        previewImage.setImageUrl("resource://" + imageResId);
        previewImage.setSortOrder(0);
        previewImage.setImageType(0);
        previewImages.add(previewImage);
        theme.setPreviewImages(previewImages);

        // 标记为默认主题
        theme.setClassify(0);

        // 标记为已下载（默认主题总是可用的）
        theme.setIsDownloaded(true);

        // 检查是否为当前使用的主题
        boolean isCurrentlyUsed = isThemeCurrentlyInUse(theme);
        theme.setIsCurrentTheme(isCurrentlyUsed);
        theme.setUse(isCurrentlyUsed); // 同时设置use字段

        return theme;
    }

    /**
     * 检查主题是否正在使用 - 参考LocalThemeDetailScene的实现
     */
    private boolean isThemeCurrentlyInUse(UnifiedThemeModel themeInfo) {
        if (themeInfo == null) return false;

        // 获取当前设置的主题
        String currentDayTheme = SettingsManager.getDayTheme();
        String currentNightTheme = SettingsManager.getNightTheme();

        // 构建主题文件名
        String themeFileName = themeInfo.getThemeName() + ".skin";

        MyLog.d(TAG, "判断主题使用状态:");
        MyLog.d(TAG, "  - 主题名称: " + themeInfo.getThemeName());
        MyLog.d(TAG, "  - 主题类型: " + themeInfo.getThemeType() + " (0=夜间, 1=白天)");
        MyLog.d(TAG, "  - 主题分类: " + themeInfo.getClassify() + " (0=默认主题, 1=用户主题)");
        MyLog.d(TAG, "  - 当前白天主题: " + currentDayTheme);
        MyLog.d(TAG, "  - 当前夜间主题: " + currentNightTheme);
        MyLog.d(TAG, "  - 主题文件名: " + themeFileName);

        boolean isInUse = false;

        // 对于默认主题的特殊处理 - 使用classify字段标识默认主题
        if (themeInfo.getClassify() != null && themeInfo.getClassify() == 0) {
            if (themeInfo.getThemeType() == 0) {
                // 默认白天主题：检查白天主题设置是否为空
                isInUse = currentDayTheme.isEmpty() ||
                         currentDayTheme.contains("默认白天主题");
                MyLog.d(TAG, "  - 默认白天主题判断结果: " + isInUse);
            } else {
                // 默认夜间主题：检查夜间主题设置是否为空或为默认值
                isInUse = currentNightTheme.isEmpty() ||
                         currentNightTheme.equals(SettingsConstants.DEFAULT_NIGHT_THEME) ||
                         currentNightTheme.contains("默认夜间主题");
                MyLog.d(TAG, "  - 默认夜间主题判断结果: " + isInUse);
            }
        } else {
            // 用户下载的主题：检查主题文件名是否匹配
            if (themeInfo.getThemeType() == 0) {
                // 夜间主题
                isInUse = currentNightTheme.equals(themeFileName) ||
                         currentNightTheme.contains(themeInfo.getThemeName());
                MyLog.d(TAG, "  - 用户夜间主题判断结果: " + isInUse);
            } else {
                // 白天主题
                isInUse = currentDayTheme.equals(themeFileName) ||
                         currentDayTheme.contains(themeInfo.getThemeName());
                MyLog.d(TAG, "  - 用户白天主题判断结果: " + isInUse);
            }
        }

        MyLog.d(TAG, "  - 最终判断结果: " + isInUse);
        return isInUse;
    }

    /**
     * 更新适配器数据
     */
    private void updateAdapterData() {
        // 有数据时禁用空视图，确保正常显示数据
        mThemeAdapter.setStateViewEnable(false);
        mThemeAdapter.submitList(new ArrayList<>(mLocalThemeDataList));
    }


    /**
     * 处理主题项点击事件
     */
    private void handleThemeItemClick(BaseQuickAdapter<?, ?> adapter, View view, int position) {
        if (isValidPosition(position)) {
            UnifiedThemeModel themeInfo = mLocalThemeDataList.get(position);
            // 对于本地主题，点击应该打开详情页面而不是直接应用
            navigateToThemeDetail(themeInfo);
        }
    }

    /**
     * 处理主题项长按事件
     */
    private boolean handleThemeItemLongClick(BaseQuickAdapter<?, ?> adapter, View view, int position) {
        if (isValidPosition(position)) {
            UnifiedThemeModel themeInfo = mLocalThemeDataList.get(position);
            showThemeOptions(themeInfo);
            return true;
        }
        return false;
    }

    /**
     * 检查位置是否有效
     */
    private boolean isValidPosition(int position) {
        return position >= 0 && position < mLocalThemeDataList.size();
    }

    /**
     * 显示主题选项
     */
    private void showThemeOptions(UnifiedThemeModel themeInfo) {
        // 长按时显示操作选项，默认主题和用户主题都可以查看详情
        // 但默认主题不能删除，只能应用和查看
        navigateToThemeDetail(themeInfo);
    }

    /**
     * 导航到本地主题详情页面
     */
    private void navigateToThemeDetail(UnifiedThemeModel themeInfo) {
        // 创建共享元素动画映射
        ArrayMap<String, SceneTransition> transitionMap = new ArrayMap<>();
        transitionMap.put(LocalThemeDetailScene.VIEW_NAME_THEME_IMAGE + themeInfo.getId(), new AutoSceneTransition());
        transitionMap.put(LocalThemeDetailScene.VIEW_NAME_THEME_TITLE + themeInfo.getId(), new AutoSceneTransition());

        // 配置动画执行器
        SharedElementSceneTransitionExecutor transitionExecutor =
                new SharedElementSceneTransitionExecutor(transitionMap, new Fade());

        // 创建推送选项
        PushOptions pushOptions = new PushOptions.Builder()
                .setAnimation(transitionExecutor)
                .build();

        // 导航到本地主题详情页面
        LocalThemeDetailScene detailScene = LocalThemeDetailScene.newInstance(themeInfo);
        getNavigationScene(this).push(detailScene, pushOptions);
    }




    /**
     * 设置加载状态
     */
    private void setLoadingState(boolean isLoading) {
        if (mBinding != null) {
            mBinding.swipeRefreshLayout.setRefreshing(isLoading);
        }
    }

    /**
     * 计算最优列数 - 简化版本
     * 带鱼屏4列，横屏3列，竖屏2列
     */
    private int calculateOptimalSpanCount() {
        int screenWidth = ScreenUtils.getWindowWidth(requireActivity());
        int screenHeight = ScreenUtils.getWindowHeigh(requireActivity());
        int orientation = ScreenUtils.getScreenOrientation(screenWidth, screenHeight);

        switch (orientation) {
            case ScreenUtils.ORIENTATION_PORTRAIT:
                MyLog.d(TAG, "竖屏模式，使用2列布局");
                return 2;
            case ScreenUtils.ORIENTATION_LANDSCAPE:
                MyLog.d(TAG, "横屏模式，使用3列布局");
                return 3;
            case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                MyLog.d(TAG, "带鱼屏模式，使用3列布局");
                return 3;
            default:
                MyLog.d(TAG, "未知屏幕方向，使用默认3列布局");
                return 3;
        }
    }

    /**
     * 检查主题更新 - 从服务器获取最新主题信息 (使用新API)
     */
    @SuppressLint("CheckResult")
    private void checkThemeUpdates() {
        if (mIsCheckingUpdate) {
            MyLog.d(TAG, "正在检查更新，跳过重复请求");
            return;
        }

        mIsCheckingUpdate = true;
        MyLog.v(TAG, "开始检查主题更新 - 使用新API");

        mDisposables.add(
                RxHttp.get(Const.NEW_THEME_LIST)
                        .add("pageNum", "1")
                        .add("pageSize", "100")  // 获取更多数据用于更新检查
                        .add("status", "1")  // 只获取发布状态的主题
                        .add("releaseStatus", "2")  // 只获取正式发布的主题
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleNewServerThemeDataSuccessString,
                                this::handleServerThemeDataError
                        )
        );
    }

    /**
     * 处理新API服务器主题数据成功 - 字符串响应版本
     */
    private void handleNewServerThemeDataSuccessString(String responseString) {
        mIsCheckingUpdate = false;

        MyLog.v(TAG, "新API服务器主题数据响应: " + responseString);
//
//        try {
//            Gson gson = new Gson();
//            TypeToken<TableDataInfo<NewThemeInfo>> typeToken = new TypeToken<TableDataInfo<NewThemeInfo>>(){};
//            TableDataInfo<NewThemeInfo> tableDataInfo = gson.fromJson(responseString, typeToken.getType());
//
//            if (tableDataInfo != null && tableDataInfo.isSuccess() &&
//                tableDataInfo.getRows() != null && !tableDataInfo.getRows().isEmpty()) {
//                MyLog.v(TAG, "新API服务器主题数据解析成功，数量: " + tableDataInfo.getRows().size());
//
//                // 简化处理：新API暂时不进行更新检查，直接完成
//                MyLog.d(TAG, "新API更新检查完成");
//            } else {
//                MyLog.w(TAG, "新API服务器主题数据为空或解析失败");
//            }
//        } catch (Exception e) {
//            MyLog.e(TAG, "解析新API服务器主题数据失败", e);
//        }
    }





    /**
     * 处理服务器主题数据错误
     */
    private void handleServerThemeDataError(Throwable throwable) {
        mIsCheckingUpdate = false;
        MyLog.e(TAG, "检查主题更新失败", throwable);
        // 不显示错误提示，静默失败
    }


    /**
     * 检查并更新主题版本状态 - 参考老逻辑的思路
     */
    private void checkAndUpdateThemeVersions() {
        if (mServerThemeList.isEmpty()) {
            MyLog.d(TAG, "服务器主题列表为空，跳过版本检查");
            return;
        }

        boolean hasUpdates = false;

        for (UnifiedThemeModel localTheme : mLocalThemeDataList) {
            // 跳过默认主题
            if (localTheme.getClassify() != null && localTheme.getClassify() == 0) {
                continue;
            }

            // 查找对应的服务器主题
            UnifiedThemeModel serverTheme = findServerThemeById(localTheme.getId());
            if (serverTheme != null && serverTheme.getThemePackage() != null && localTheme.getThemePackage() != null) {
                // 比较版本号，参考老逻辑
                Integer localVersionCode = localTheme.getThemePackage().getVersionCode();
                Integer serverVersionCode = serverTheme.getThemePackage().getVersionCode();
                
                if (localVersionCode == null) localVersionCode = 1;
                if (serverVersionCode == null) serverVersionCode = 1;
                
                boolean needUpdate = serverVersionCode > localVersionCode;

                // 检查更新状态是否发生变化
                Boolean currentUpdateStatus = localTheme.getHasUpdate();
                if (currentUpdateStatus == null) currentUpdateStatus = false;
                
                if (needUpdate != currentUpdateStatus) {
                    localTheme.setHasUpdate(needUpdate);

                    // 更新服务器的版本信息到本地主题
                    if (needUpdate) {
                        // 更新主题包信息但保持本地下载状态
                        updateLocalThemeWithServerPackage(localTheme, serverTheme);
                        hasUpdates = true;

                        MyLog.d(TAG, "发现主题更新: " + localTheme.getThemeName() +
                                " 本地版本: v" + localVersionCode +
                                " 服务器版本: v" + serverVersionCode);
                    }
                }
            }
        }

        if (hasUpdates) {
            MyLog.d(TAG, "发现主题更新，刷新列表显示");
            updateAdapterData();
            
            // 保存更新状态到数据库
            saveThemeUpdateStatus();
        } else {
            MyLog.d(TAG, "所有主题都是最新版本");
        }
    }

    /**
     * 更新本地主题的服务器包信息 - 保持本地状态
     */
    private void updateLocalThemeWithServerPackage(UnifiedThemeModel localTheme, UnifiedThemeModel serverTheme) {
        // 保存当前的本地状态
        Boolean isDownloaded = localTheme.getIsDownloaded();
        String localFilePath = localTheme.getLocalFilePath();
        String downloadTime = localTheme.getDownloadTime();
        
        // 更新主题包信息
        localTheme.setThemePackage(serverTheme.getThemePackage());
        
        // 更新其他服务器信息
        if (serverTheme.getDownloadCount() != null) {
            localTheme.setDownloadCount(serverTheme.getDownloadCount());
        }
        
        if (serverTheme.getThemeDescription() != null && !serverTheme.getThemeDescription().trim().isEmpty()) {
            localTheme.setThemeDescription(serverTheme.getThemeDescription());
        }
        
        if (serverTheme.getLabel() != null && !serverTheme.getLabel().trim().isEmpty()) {
            localTheme.setLabel(serverTheme.getLabel());
        }
        
        // 恢复本地状态
        localTheme.setIsDownloaded(isDownloaded);
        localTheme.setLocalFilePath(localFilePath);
        localTheme.setDownloadTime(downloadTime);
    }

    /**
     * 保存主题更新状态到数据库
     */
    private void saveThemeUpdateStatus() {
        for (UnifiedThemeModel theme : mLocalThemeDataList) {
            if (theme.getClassify() != null && theme.getClassify() != 0 && 
                theme.getId() != null && Boolean.TRUE.equals(theme.getHasUpdate())) {
                
                // 异步保存到数据库
                mDisposables.add(
                    mDbManager.update(theme)
                        .subscribe(
                            result -> {
                                MyLog.v(TAG, "主题更新状态保存成功: " + theme.getThemeName());
                            },
                            throwable -> {
                                MyLog.e(TAG, "主题更新状态保存失败: " + theme.getThemeName(), throwable);
                            }
                        )
                );
            }
        }
    }

    /**
     * 根据ID查找服务器主题
     */
    private UnifiedThemeModel findServerThemeById(Long themeId) {
        for (UnifiedThemeModel serverTheme : mServerThemeList) {
            if (serverTheme.getId() != null && serverTheme.getId().equals(themeId)) {
                return serverTheme;
            }
        }
        return null;
    }

    @Override
    public void onResume() {
        super.onResume();
        // 页面重新可见时刷新数据，确保主题使用状态是最新的
        MyLog.d(TAG, "页面恢复，刷新主题使用状态");
        refreshThemeUsageStatus();
    }

    /**
     * 刷新主题使用状态 - 优化版本，参考ThemeDetailScene的状态管理
     */
    private void refreshThemeUsageStatus() {
        MyLog.d(TAG, "开始刷新主题使用状态，主题数量: " + mLocalThemeDataList.size());
        
        boolean hasStatusChanged = false;
        int updatedCount = 0;
        
        // 重新检查所有主题的使用状态
        for (UnifiedThemeModel theme : mLocalThemeDataList) {
            // 保存旧的使用状态
            Boolean oldCurrentTheme = theme.getIsCurrentTheme();
            Boolean oldUse = theme.isUse();
            
            // 重新检查使用状态
            boolean isCurrentlyUsed = isThemeCurrentlyInUse(theme);
            
            // 更新状态
            theme.setIsCurrentTheme(isCurrentlyUsed);
            theme.setUse(isCurrentlyUsed);
            
            // 检查状态是否发生变化
            if (!Objects.equals(oldCurrentTheme, isCurrentlyUsed) || !Objects.equals(oldUse, isCurrentlyUsed)) {
                hasStatusChanged = true;
                updatedCount++;
                
                MyLog.d(TAG, "主题状态更新: " + theme.getThemeName() + 
                    " [使用状态: " + oldCurrentTheme + " -> " + isCurrentlyUsed + "]");
                
                // 更新数据库中的使用状态（仅对非默认主题）
                updateThemeStatusInDatabase(theme, isCurrentlyUsed);
                
                // 重新计算推荐度（因为使用状态影响推荐度）
                calculateThemeRecommendScore(theme);
            }
        }

        // 如果有状态变化，重新排序和刷新显示
        if (hasStatusChanged) {
            MyLog.d(TAG, "检测到主题状态变化，重新排序并刷新显示，更新数量: " + updatedCount);
            
            // 重新排序
            sortThemesByRecommendation();
            
            // 刷新适配器显示
            updateAdapterData();
            
            // 更新统计信息
            showThemeStatistics();
        } else {
            MyLog.d(TAG, "主题使用状态无变化");
        }
    }

    /**
     * 更新数据库中的主题状态 - 参考ThemeDetailScene的数据库更新逻辑
     */
    private void updateThemeStatusInDatabase(UnifiedThemeModel theme, boolean isCurrentlyUsed) {
        if (theme.getClassify() != null && theme.getClassify() == 0) {
            // 默认主题不需要更新数据库
            return;
        }
        
        if (theme.getId() == null) {
            MyLog.w(TAG, "主题ID为空，跳过数据库更新: " + theme.getThemeName());
            return;
        }
        
        // 异步更新数据库
        mDisposables.add(
            mDbManager.updateUsageStatus(theme.getId(), isCurrentlyUsed)
                .subscribe(
                    result -> {
                        MyLog.v(TAG, "主题使用状态数据库更新成功: " + theme.getThemeName() + 
                            ", 影响行数: " + result);
                    },
                    throwable -> {
                        MyLog.e(TAG, "主题使用状态数据库更新失败: " + theme.getThemeName(), throwable);
                        // 数据库更新失败不影响UI显示
                    }
                )
        );
    }

    /**
     * 检查主题更新状态 - 参考ThemeDetailScene的更新检查逻辑
     */
    private void checkThemeUpdatesEnhanced() {
        if (mIsCheckingUpdate) {
            MyLog.d(TAG, "正在检查更新，跳过重复请求");
            return;
        }

        // 统计需要检查更新的主题
        List<UnifiedThemeModel> themesToCheck = new ArrayList<>();
        for (UnifiedThemeModel theme : mLocalThemeDataList) {
            // 只检查用户下载的主题
            if (theme.getClassify() != null && theme.getClassify() != 0 && 
                theme.getId() != null && theme.getId() > 0) {
                themesToCheck.add(theme);
            }
        }
        
        if (themesToCheck.isEmpty()) {
            MyLog.d(TAG, "没有需要检查更新的用户主题");
            return;
        }

        mIsCheckingUpdate = true;
        MyLog.v(TAG, "开始检查 " + themesToCheck.size() + " 个用户主题的更新状态");

        // 批量检查主题更新（参考FindThemeScene的批量处理逻辑）
        checkThemeUpdatesBatch(themesToCheck);
    }

    /**
     * 批量检查主题更新 - 参考推荐主题的批量处理逻辑
     */
    @SuppressLint("CheckResult")
    private void checkThemeUpdatesBatch(List<UnifiedThemeModel> themesToCheck) {
        int totalThemes = themesToCheck.size();
        MyLog.d(TAG, "开始批量检查 " + totalThemes + " 个主题的更新");
        
        // 计数器，用于跟踪完成状态
        final int[] completedCount = {0};
        final int[] updatedCount = {0};
        
        // 使用新API批量检查主题详情
        for (UnifiedThemeModel theme : themesToCheck) {
            mDisposables.add(
                RxHttp.get(Const.NEW_THEME_DETAIL + theme.getId())
                    .toObservable(String.class)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(
                        responseString -> {
                            boolean hasUpdate = handleThemeUpdateCheckSuccess(theme, responseString);
                            if (hasUpdate) {
                                updatedCount[0]++;
                            }
                            
                            completedCount[0]++;
                            checkBatchUpdateComplete(completedCount[0], totalThemes, updatedCount[0]);
                        },
                        throwable -> {
                            handleThemeUpdateCheckError(theme, throwable);
                            completedCount[0]++;
                            checkBatchUpdateComplete(completedCount[0], totalThemes, updatedCount[0]);
                        }
                    )
            );
        }
    }

    /**
     * 检查批量更新是否完成
     */
    private void checkBatchUpdateComplete(int completed, int total, int updated) {
        if (completed >= total) {
            finishUpdateCheck();
            
            if (updated > 0) {
                MyLog.d(TAG, "批量更新检查完成，发现 " + updated + " 个主题有更新");
                // 刷新显示
                updateAdapterData();
                showThemeStatistics();
            } else {
                MyLog.d(TAG, "批量更新检查完成，所有主题都是最新版本");
            }
        }
    }

    /**
     * 处理主题更新检查成功 - 参考ThemeDetailScene的更新处理逻辑
     */
    private boolean handleThemeUpdateCheckSuccess(UnifiedThemeModel localTheme, String responseString) {
        try {
            Gson gson = new Gson();
            TypeToken<ApiResponse<UnifiedThemeModel>> typeToken = new TypeToken<ApiResponse<UnifiedThemeModel>>(){};
            ApiResponse<UnifiedThemeModel> response = gson.fromJson(responseString, typeToken.getType());

            if (response != null && response.isSuccess() && response.getData() != null) {
                UnifiedThemeModel serverTheme = response.getData();
                
                // 检查是否需要更新
                boolean needUpdate = checkIfThemeNeedsUpdate(localTheme, serverTheme);
                
                if (needUpdate) {
                    // 更新本地主题信息
                    updateLocalThemeWithServerInfo(localTheme, serverTheme);
                    
                    MyLog.d(TAG, "发现主题更新: " + localTheme.getThemeName());
                    return true;
                } else {
                    MyLog.d(TAG, "主题已是最新版本: " + localTheme.getThemeName());
                }
            }
        } catch (Exception e) {
            MyLog.e(TAG, "解析主题更新响应失败: " + localTheme.getThemeName(), e);
        }
        
        return false;
    }

    /**
     * 检查主题是否需要更新 - 参考ThemeDetailScene的版本比较逻辑
     */
    private boolean checkIfThemeNeedsUpdate(UnifiedThemeModel localTheme, UnifiedThemeModel serverTheme) {
        // 检查主题包版本
        UnifiedThemeModel.ThemePackage localPackage = localTheme.getThemePackage();
        UnifiedThemeModel.ThemePackage serverPackage = serverTheme.getThemePackage();
        
        if (localPackage == null || serverPackage == null) {
            return false; // 无法比较版本
        }
        
        Integer localVersionCode = localPackage.getVersionCode();
        Integer serverVersionCode = serverPackage.getVersionCode();
        
        if (localVersionCode == null) {
            localVersionCode = 1;
        }
        if (serverVersionCode == null) {
            serverVersionCode = 1;
        }
        
        return serverVersionCode > localVersionCode;
    }

    /**
     * 用服务器信息更新本地主题 - 参考ThemeDetailScene的信息更新逻辑
     */
    private void updateLocalThemeWithServerInfo(UnifiedThemeModel localTheme, UnifiedThemeModel serverTheme) {
        // 更新主题包信息
        if (serverTheme.getThemePackage() != null) {
            localTheme.setThemePackage(serverTheme.getThemePackage());
        }
        
        // 更新下载统计信息
        if (serverTheme.getDownloadCount() != null) {
            localTheme.setDownloadCount(serverTheme.getDownloadCount());
        }
        
        if (serverTheme.getHeat() != null) {
            // 保留本地计算的推荐度，不被服务器数据覆盖
            // localTheme.setHeat(serverTheme.getHeat());
        }
        
        // 更新其他信息
        if (serverTheme.getThemeDescription() != null && !serverTheme.getThemeDescription().trim().isEmpty()) {
            localTheme.setThemeDescription(serverTheme.getThemeDescription());
        }
        
        if (serverTheme.getLabel() != null && !serverTheme.getLabel().trim().isEmpty()) {
            localTheme.setLabel(serverTheme.getLabel());
        }
        
        // 更新预览图片
        if (serverTheme.getPreviewImages() != null && !serverTheme.getPreviewImages().isEmpty()) {
            localTheme.setPreviewImages(serverTheme.getPreviewImages());
        }
        
        // 保存更新到数据库
        saveUpdatedThemeToDatabase(localTheme);
    }

    /**
     * 保存更新的主题到数据库 - 参考ThemeDetailScene的保存逻辑
     */
    private void saveUpdatedThemeToDatabase(UnifiedThemeModel theme) {
        mDisposables.add(
            mDbManager.update(theme)
                .subscribe(
                    result -> {
                        MyLog.v(TAG, "主题更新信息保存成功: " + theme.getThemeName() + 
                            ", 影响行数: " + result);
                        
                        // 刷新UI显示
                        updateAdapterData();
                    },
                    throwable -> {
                        MyLog.e(TAG, "主题更新信息保存失败: " + theme.getThemeName(), throwable);
                    }
                )
        );
    }

    /**
     * 处理主题更新检查失败
     */
    private void handleThemeUpdateCheckError(UnifiedThemeModel theme, Throwable throwable) {
        MyLog.e(TAG, "主题更新检查失败: " + theme.getThemeName(), throwable);
        // 静默失败，不影响用户体验
    }

    /**
     * 完成更新检查 - 统一的更新检查完成处理
     */
    private void finishUpdateCheck() {
        mIsCheckingUpdate = false;
        MyLog.d(TAG, "主题更新检查完成");
    }

    /**
     * 处理和排序本地主题 - 简化版本，直接使用数据库数据
     */
    private List<UnifiedThemeModel> processAndSortLocalThemes(List<UnifiedThemeModel> themes) {
        List<UnifiedThemeModel> processedThemes = new ArrayList<>();
        
        for (UnifiedThemeModel theme : themes) {
            // 数据完整性检查和补全（参考ThemeDetailScene的字段确保逻辑）
            ensureThemeDataCompleteness(theme);
            
            processedThemes.add(theme);
        }
        
        return processedThemes;
    }

    /**
     * 确保主题数据完整性 - 简化版本，只确保必要字段
     */
    private void ensureThemeDataCompleteness(UnifiedThemeModel theme) {
        // 确保基本字段不为空
        if (theme.getThemeName() == null || theme.getThemeName().trim().isEmpty()) {
            theme.setThemeName("主题_" + theme.getId());
        }
        
        if (theme.getAuthor() == null || theme.getAuthor().trim().isEmpty()) {
            theme.setAuthor("未知作者");
        }
        
        if (theme.getThemeDescription() == null || theme.getThemeDescription().trim().isEmpty()) {
            String defaultDesc = theme.getThemeType() == 0 ? "精美的夜间主题" : "清新的白天主题";
            theme.setThemeDescription(defaultDesc);
        }
        
        // 确保下载统计数据存在
        if (theme.getDownloadCount() == null) {
            theme.setDownloadCount(0L);
        }
        
        if (theme.getHeat() == null) {
            theme.setHeat(0L);
        }
        
        // 确保时间字段存在
        if (theme.getDownloadTime() == null || theme.getDownloadTime().trim().isEmpty()) {
            theme.setDownloadTime(String.valueOf(System.currentTimeMillis()));
        }
    }

    /**
     * 计算主题推荐度 - 参考FindThemeScene的热度算法
     */
    private void calculateThemeRecommendScore(UnifiedThemeModel theme) {
        double score = 0.0;
        
        // 基础分数
        score += 50.0;
        
        // 使用状态加分（最高优先级）
        if (Boolean.TRUE.equals(theme.getIsCurrentTheme())) {
            score += 100.0; // 当前使用的主题优先级最高
        }
        
        // 下载时间加分（最近下载的优先级高）
        if (theme.getDownloadTime() != null) {
            try {
                long downloadTime = Long.parseLong(theme.getDownloadTime());
                long daysDiff = (System.currentTimeMillis() - downloadTime) / (24 * 60 * 60 * 1000);
                
                if (daysDiff < 1) {
                    score += 30.0; // 今天下载
                } else if (daysDiff < 7) {
                    score += 20.0; // 一周内下载
                } else if (daysDiff < 30) {
                    score += 10.0; // 一月内下载
                }
            } catch (NumberFormatException e) {
                MyLog.w(TAG, "解析下载时间失败: " + theme.getDownloadTime());
            }
        }
        
        // 主题类型匹配当前时间加分
        boolean isNightMode = SkinManager.getInstance().isNightModeNow();
        if ((theme.getThemeType() == 0 && isNightMode) || (theme.getThemeType() == 1 && !isNightMode)) {
            score += 15.0; // 符合当前模式的主题
        }
        
        // 原始下载量和热度加分
        if (theme.getDownloadCount() != null && theme.getDownloadCount() > 0) {
            score += Math.min(theme.getDownloadCount() * 0.1, 20.0); // 最多加20分
        }
        
        if (theme.getHeat() != null && theme.getHeat() > 0) {
            score += Math.min(theme.getHeat() * 0.05, 10.0); // 最多加10分
        }
        
        // 将推荐度存储在heat字段中（复用现有字段）
        theme.setHeat((long) score);
        
        MyLog.d(TAG, "主题推荐度计算: " + theme.getThemeName() + " -> " + score);
    }

    /**
     * 对主题进行智能排序 - 参考FindThemeScene的排序逻辑
     */
    private void sortThemesByRecommendation() {
        // 对用户主题进行排序（保持默认主题在前面）
        List<UnifiedThemeModel> defaultThemes = new ArrayList<>();
        List<UnifiedThemeModel> userThemes = new ArrayList<>();
        
        // 分离默认主题和用户主题
        for (UnifiedThemeModel theme : mLocalThemeDataList) {
            if (theme.getClassify() != null && theme.getClassify() == 0) {
                defaultThemes.add(theme);
            } else {
                userThemes.add(theme);
            }
        }
        
        // 对用户主题按推荐度排序
        userThemes.sort((a, b) -> {
            // 首先按使用状态排序（正在使用的在前）
            boolean aInUse = Boolean.TRUE.equals(a.getIsCurrentTheme());
            boolean bInUse = Boolean.TRUE.equals(b.getIsCurrentTheme());
            
            if (aInUse != bInUse) {
                return aInUse ? -1 : 1;
            }
            
            // 然后按推荐度排序（heat字段存储推荐度）
            Long aScore = a.getHeat() != null ? a.getHeat() : 0L;
            Long bScore = b.getHeat() != null ? b.getHeat() : 0L;
            
            if (!aScore.equals(bScore)) {
                return Long.compare(bScore, aScore); // 降序排列
            }
            
            // 最后按下载时间排序（最近下载的在前）
            try {
                long aTime = a.getDownloadTime() != null ? Long.parseLong(a.getDownloadTime()) : 0L;
                long bTime = b.getDownloadTime() != null ? Long.parseLong(b.getDownloadTime()) : 0L;
                return Long.compare(bTime, aTime); // 降序排列
            } catch (NumberFormatException e) {
                return 0;
            }
        });
        
        // 重新组合列表：默认主题 + 排序后的用户主题
        mLocalThemeDataList.clear();
        mLocalThemeDataList.addAll(defaultThemes);
        mLocalThemeDataList.addAll(userThemes);
        
        MyLog.d(TAG, "主题智能排序完成，默认主题: " + defaultThemes.size() + ", 用户主题: " + userThemes.size());
    }

    /**
     * 显示主题统计信息 - 参考FindThemeScene的友好提示
     */
    private void showThemeStatistics() {
        int totalThemes = mLocalThemeDataList.size();
        int userThemes = 0;
        int currentlyUsedThemes = 0;
        
        for (UnifiedThemeModel theme : mLocalThemeDataList) {
            if (theme.getClassify() != null && theme.getClassify() != 0) {
                userThemes++;
            }
            if (Boolean.TRUE.equals(theme.getIsCurrentTheme())) {
                currentlyUsedThemes++;
            }
        }
        
        // 构建友好的统计信息
        StringBuilder statsMessage = new StringBuilder();
        if (userThemes > 0) {
            statsMessage.append("共 ").append(userThemes).append(" 个下载主题");
            if (currentlyUsedThemes > 0) {
                statsMessage.append("，").append(currentlyUsedThemes).append(" 个正在使用");
            }
        } else {
            statsMessage.append("暂无下载的主题，可到「推荐主题」中下载喜欢的主题");
        }
        
        MyLog.d(TAG, "主题统计: " + statsMessage.toString());
        
        // 如果有用户主题，显示优化提示
        if (userThemes > 0) {
            // 不显示toast，避免打扰用户，只记录日志
            MyLog.d(TAG, "本地主题已按智能推荐算法排序");
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        mDisposables.clear();
        mBinding = null;
    }
}
