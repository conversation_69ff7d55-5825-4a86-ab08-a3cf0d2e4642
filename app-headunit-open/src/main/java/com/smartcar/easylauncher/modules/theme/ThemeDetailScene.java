package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;

import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeResponse;
import com.smartcar.easylauncher.data.model.theme.api.ThemeListModel;
import com.smartcar.easylauncher.shared.adapter.theme.ThemeBannerAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneThemeDetailBinding;
import com.smartcar.easylauncher.data.database.dbmager.ThemeDbManager;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.theme.ThemeBannerModel;
import com.smartcar.easylauncher.data.model.theme.api.ApiResponse;
import com.smartcar.easylauncher.core.constants.Const;
import com.google.gson.reflect.TypeToken;

import io.reactivex.rxjava3.core.Observable;
import rxhttp.RxHttp;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;

import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.shared.utils.file.FileUtils;
import com.smartcar.easylauncher.shared.view.SecondaryPageActionBar;
import com.zhpan.bannerview.constants.IndicatorGravity;
import com.zhpan.bannerview.constants.PageStyle;
import com.zhpan.bannerview.utils.BannerUtils;
import com.zhpan.indicator.enums.IndicatorSlideMode;
import com.zhpan.indicator.enums.IndicatorStyle;

import java.io.File;

import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import rxhttp.wrapper.cache.CacheMode;


/**
 * 主题详情Scene
 * 显示主题的详细信息，支持预览、下载、应用和删除操作
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class ThemeDetailScene extends BaseScene {

    private static final String TAG = "ThemeDetailScene";
    private static final String KEY_UNIFIED_THEME = "unified_theme";

    // 共享元素动画常量
    public static final String VIEW_NAME_THEME_IMAGE = "theme:detail:image";
    public static final String VIEW_NAME_THEME_TITLE = "theme:detail:title";

    private SceneThemeDetailBinding binding;
    private UnifiedThemeModel themeInfo; // 使用统一主题模型
    private final CompositeDisposable disposables = new CompositeDisposable();
    private ThemeDbManager dbManager;
    private boolean isDownloading = false;

    // 统一操作栏组件
    private SecondaryPageActionBar actionBar;

    private ThemeBannerAdapter bannerAdapter;
    private int currentBannerItemCount = 0;

    // 操作状态管理
    private boolean isOperationInProgress = false;
    private long lastClickTime = 0;
    private static final long CLICK_DEBOUNCE_TIME = 1000; // 1秒防抖

    // 下载相关
    private Disposable downloadDisposable;
    private String downloadTimeMillis;
    private ThemeListModel.RowsDTO themeListInfo;

    /**
     * 创建主题详情Scene实例 - 使用统一主题模型
     */
    public static ThemeDetailScene newInstance(ThemeListModel.RowsDTO theme) {
        ThemeDetailScene scene = new ThemeDetailScene();
        Bundle args = new Bundle();
        args.putString(KEY_UNIFIED_THEME, new Gson().toJson(theme));
        scene.setArguments(args);
        return scene;
    }

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SceneThemeDetailBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // 初始化流程：数据 -> 视图 -> 共享元素 -> 加载详情
        initData();
        initViews();
        setupSharedElements();
        loadThemeDetailFromAPI();
    }

    // ================================ 数据管理 ================================

    /**
     * 初始化数据 - 从Bundle中解析主题基本信息
     */
    private void initData() {
        dbManager = ThemeDbManager.getInstance();

        // 解析从列表页传递过来的主题基本信息
        Bundle args = getArguments();
        if (args != null) {
            String themeJson = args.getString(KEY_UNIFIED_THEME);
            if (themeJson != null) {
                themeListInfo = new Gson().fromJson(themeJson, ThemeListModel.RowsDTO.class);
                
                // 将列表数据转换为详情数据模型（用于UI显示和共享元素）
                convertListInfoToThemeInfo();
            }
        }

        if (themeListInfo == null || themeInfo == null) {
            MyLog.e(TAG, "主题信息为空，返回上一页");
            getNavigationScene(this).pop();
        }
    }

    /**
     * 将列表数据转换为详情数据模型
     * 目的：统一数据模型，避免两套数据源的混乱
     */
    private void convertListInfoToThemeInfo() {
        if (themeListInfo == null) return;
        
        // 创建临时的UnifiedThemeModel用于初始显示
        themeInfo = new UnifiedThemeModel();
        
        // 类型安全的ID转换
        if (themeListInfo.getId() != null) {
            themeInfo.setId(themeListInfo.getId().longValue());
        }
        
        themeInfo.setThemeName(themeListInfo.getThemeName());
        themeInfo.setThemeDescription(themeListInfo.getThemeDescription());
        themeInfo.setAuthor(themeListInfo.getAuthor());
        themeInfo.setThemeType(themeListInfo.getThemeType());
        
        // 类型安全的下载量转换
        if (themeListInfo.getDownloadCount() != null) {
            themeInfo.setDownloadCount(themeListInfo.getDownloadCount().longValue());
        }
        
        themeInfo.setLabel(themeListInfo.getLabel());
        themeInfo.setCoverImage(themeListInfo.getCoverImage());
        
        // 设置默认值，避免空指针
        themeInfo.setIsDownloaded(false);
        themeInfo.setIsCurrentTheme(false);
        
        MyLog.d(TAG, "列表数据转换完成，主题: " + themeInfo.getThemeName());
    }

    // ================================ 视图初始化 ================================

    /**
     * 初始化视图组件
     */
    private void initViews() {
        initActionBar();
        initBannerView();
        
        // 使用转换后的基本数据先显示UI，提升用户体验
        setupThemeBasicInfo();
    }

    /**
     * 初始化操作栏
     */
    private void initActionBar() {
        actionBar = binding.actionBar;
        actionBar.setBackAction(() -> getNavigationScene(this).pop());
        
        // 初始状态：显示加载中
        updateActionBarToLoading();
    }

    /**
     * 设置操作栏为加载状态
     */
    private void updateActionBarToLoading() {
        if (actionBar != null) {
            actionBar.setPrimaryAction("加载中...", () -> {
                // 加载状态下禁用点击
            });
            actionBar.setPrimaryActionEnabled(false);
            actionBar.hideSecondaryAction();
        }
    }

    // ================================ 共享元素动画 ================================

    /**
     * 设置共享元素动画
     * 统一的共享元素设置方法，根据当前可用的数据设置
     */
    private void setupSharedElements() {
        if (themeInfo != null && themeInfo.getId() != null) {
            // 设置轮播图的共享元素名称
            ViewCompat.setTransitionName(binding.bannerViewPager, 
                VIEW_NAME_THEME_IMAGE + themeInfo.getId());
            
            // 设置主题名称的共享元素名称
            ViewCompat.setTransitionName(binding.tvThemeName, 
                VIEW_NAME_THEME_TITLE + themeInfo.getId());
            
            MyLog.d(TAG, "设置共享元素动画完成，主题ID: " + themeInfo.getId());
        } else {
            MyLog.w(TAG, "无法设置共享元素动画，主题信息不完整");
        }
    }

    // ================================ 数据加载 ================================

    /**
     * 从API加载完整的主题详情
     */
    private void loadThemeDetailFromAPI() {
        if (themeListInfo == null) {
            MyLog.e(TAG, "无法加载详情：列表信息为空");
            return;
        }

        MyLog.d(TAG, "开始加载主题详情: " + themeListInfo.getThemeName());

        disposables.add(
                RxHttp.get(Const.NEW_THEME_DETAIL + themeListInfo.getId())
                        .setCacheMode(CacheMode.ONLY_NETWORK)
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleThemeDetailSuccess,
                                this::handleThemeDetailError
                        )
        );
    }

    /**
     * 处理主题详情加载成功
     */
    private void handleThemeDetailSuccess(String responseString) {
        MyLog.v(TAG, "主题详情API响应成功");

        try {
            UnifiedThemeResponse response = new Gson().fromJson(responseString, UnifiedThemeResponse.class);

            if (response != null && response.isSuccess() && response.getData() != null) {
                // 更新为完整的主题信息
                UnifiedThemeModel completeThemeInfo = response.getData();
                updateThemeInfo(completeThemeInfo);
                
                MyLog.v(TAG, "主题详情更新完成: " + themeInfo.getThemeName());
            } else {
                MyLog.w(TAG, "API响应异常，使用基本信息: " + 
                    (response != null ? response.getMsg() : "响应为空"));
                handleApiDataUnavailable();
            }
        } catch (Exception e) {
            MyLog.e(TAG, "解析主题详情失败，使用基本信息", e);
            handleApiDataUnavailable();
        }
    }

    /**
     * 处理主题详情加载失败
     */
    private void handleThemeDetailError(Throwable throwable) {
        MyLog.e(TAG, "主题详情加载失败，使用基本信息", throwable);
        handleApiDataUnavailable();
    }

    /**
     * 当API数据不可用时的处理
     * 使用基本信息继续显示，确保用户体验
     */
    private void handleApiDataUnavailable() {
        // 使用已有的基本信息，更新UI
        setupThemeCompleteInfo();
        updateActionBar();
        
        // 显示轮播图（使用封面图片）
        loadThemeBanner();
        
        MyLog.d(TAG, "使用基本信息显示主题详情");
    }

    /**
     * 更新主题完整信息
     * 当API数据加载成功时调用
     */
    private void updateThemeInfo(UnifiedThemeModel completeInfo) {
        this.themeInfo = completeInfo;
        
        // 重新设置共享元素（ID可能变化）
        setupSharedElements();
        
        // 更新完整的UI信息
        setupThemeCompleteInfo();
        
        // 更新操作按钮状态
        updateActionBar();
        
        // 加载完整的轮播图
        loadThemeBanner();
    }

    // ================================ UI更新 ================================

    /**
     * 设置主题基本信息
     * 使用列表数据快速显示基本信息，提升响应速度
     */
    private void setupThemeBasicInfo() {
        if (themeInfo == null) return;

        MyLog.d(TAG, "显示主题基本信息");
        
        // 设置基本信息
        binding.tvThemeName.setText(themeInfo.getThemeName());
        binding.tvThemeAuthor.setText("作者: " + (themeInfo.getAuthor() != null ? themeInfo.getAuthor() : "未知"));
        binding.tvThemeDescription.setText(themeInfo.getThemeDescription() != null ? 
            themeInfo.getThemeDescription() : "暂无描述");

        // 设置主题类型
        String themeTypeText = getThemeTypeText(themeInfo.getThemeType());
        binding.tvThemeType.setText(themeTypeText);
        
        // 设置下载量
        long downloadCount = themeInfo.getDownloadCount() != null ? themeInfo.getDownloadCount() : 0;
        binding.tvDownloadCount.setText("下载量: " + downloadCount);

        // 设置标签
        setupThemeLabel();
    }

    /**
     * 设置主题完整信息
     * 当API数据加载完成后调用，显示完整信息
     */
    private void setupThemeCompleteInfo() {
        if (themeInfo == null) return;

        MyLog.d(TAG, "显示主题完整信息");
        
        // 更新基本信息（可能有变化）
        setupThemeBasicInfo();
        
        // 设置版本信息
        setupVersionInfo();
    }

    /**
     * 获取主题类型文本
     */
    private String getThemeTypeText(Integer themeType) {
        if (themeType == null) return "未知类型";
        return themeType == 0 ? "白天主题" : "夜间主题";
    }

    /**
     * 设置主题标签
     */
    private void setupThemeLabel() {
        if (themeInfo.getLabel() != null && !themeInfo.getLabel().trim().isEmpty()) {
            binding.tvThemeLabel.setText(themeInfo.getLabel());
            binding.tvThemeLabel.setVisibility(View.VISIBLE);
        } else {
            binding.tvThemeLabel.setVisibility(View.GONE);
        }
    }

    /**
     * 设置版本信息
     */
    private void setupVersionInfo() {
        if (themeInfo.getThemePackage() != null) {
            String versionName = themeInfo.getThemePackage().getVersionName();
            if (versionName != null) {
                binding.tvThemeVersion.setText("版本: " + versionName);
                binding.tvUpdateVersion.setText("版本 v" + versionName);
            }

            // 设置更新描述
            String updateDescription = themeInfo.getThemePackage().getUpdateDescription();
            if (updateDescription != null && !updateDescription.trim().isEmpty()) {
                String formattedContent = formatUpdateContent(updateDescription);
                binding.tvUpdateContent.setText(formattedContent);
            }
        }
    }

    /**
     * 格式化更新内容为美观的列表形式
     */
    private String formatUpdateContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        // 如果内容已经包含项目符号，直接返回
        if (content.contains("•") || content.contains("·") || content.contains("-")) {
            return content;
        }

        // 按行分割并添加项目符号
        String[] lines = content.split("\n");
        StringBuilder formatted = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty()) {
                // 移除可能存在的数字序号
                line = line.replaceFirst("^\\d+\\.\\s*", "");
                formatted.append("• ").append(line);
                if (i < lines.length - 1) {
                    formatted.append("\n");
                }
            }
        }

        return formatted.toString();
    }

    // ================================ 轮播图管理 ================================

    /**
     * 初始化轮播图
     */
    private void initBannerView() {
        bannerAdapter = new ThemeBannerAdapter();
        setupBannerConfiguration();
    }

    /**
     * 配置轮播图基本设置
     */
    private void setupBannerConfiguration() {
        try {
            binding.bannerViewPager
                    .setAdapter(bannerAdapter)
                    .setUserInputEnabled(true)
                    .setScrollDuration(400)
                    .setPageStyle(PageStyle.NORMAL)
                    .setRevealWidth(0)
                    .setPageMargin(0)
                    // 指示器配置
                    .setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                    .setIndicatorSlideMode(IndicatorSlideMode.WORM)
                    .setIndicatorGravity(IndicatorGravity.CENTER)
                    .setIndicatorSliderWidth(BannerUtils.dp2px(8), BannerUtils.dp2px(24))
                    .setIndicatorSliderGap(BannerUtils.dp2px(4))
                    .setIndicatorSliderRadius(BannerUtils.dp2px(4))
                    .setIndicatorSliderColor(
                            Color.parseColor("#333333"),     // 选中色
                            Color.parseColor("#4DFFFFFF")    // 未选中色
                    )
                    .setIndicatorVisibility(View.VISIBLE)
                    .showIndicatorWhenOneItem(false)
                    .setIndicatorMargin(0, 0, 0, BannerUtils.dp2px(20))
                    // 轮播行为
                    .setAutoPlay(false)
                    .setCanLoop(false)
                    .setInterval(4000)
                    .stopLoopWhenDetachedFromWindow(true)
                    .registerLifecycleObserver(getLifecycle());

            MyLog.d(TAG, "轮播图配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "轮播图配置失败", e);
        }
    }

    /**
     * 加载主题轮播图
     */
    private void loadThemeBanner() {
        if (themeInfo == null) {
            MyLog.w(TAG, "主题信息为空，无法加载轮播图");
            return;
        }

        MyLog.d(TAG, "开始加载主题轮播图");

        try {
            java.util.List<ThemeBannerModel.BannerItem> bannerItems = createBannerItems();
            
            if (bannerItems.isEmpty()) {
                MyLog.w(TAG, "轮播图数据为空，使用默认数据");
                bannerItems = createDefaultBannerItems();
            }

            setupBannerWithItems(bannerItems);
            
        } catch (Exception e) {
            MyLog.e(TAG, "加载轮播图失败，使用默认数据", e);
            setupBannerWithItems(createDefaultBannerItems());
        }
    }

    /**
     * 创建轮播图数据项
     */
    private java.util.List<ThemeBannerModel.BannerItem> createBannerItems() {
        // 优先使用预览图片
        if (themeInfo.getPreviewImages() != null && !themeInfo.getPreviewImages().isEmpty()) {
            MyLog.d(TAG, "使用预览图片创建轮播图，数量: " + themeInfo.getPreviewImages().size());
            return createBannerItemsFromPreviewImages(themeInfo.getPreviewImages());
        }
        
        // 回退到封面图片
        if (themeInfo.getCoverImage() != null && !themeInfo.getCoverImage().isEmpty()) {
            MyLog.d(TAG, "使用封面图片创建轮播图: " + themeInfo.getCoverImage());
            return ThemeBannerModel.createFromThemeInfo(themeInfo.getCoverImage());
        }

        return new java.util.ArrayList<>();
    }

    /**
     * 从预览图片列表创建轮播图数据
     */
    private java.util.List<ThemeBannerModel.BannerItem> createBannerItemsFromPreviewImages(
            java.util.List<UnifiedThemeModel.PreviewImage> previewImages) {
        
        java.util.List<ThemeBannerModel.BannerItem> bannerItems = new java.util.ArrayList<>();

        // 直接使用API返回的顺序，无需额外排序
        for (UnifiedThemeModel.PreviewImage previewImage : previewImages) {
            String imageUrl = buildImageUrl(previewImage.getImageUrl());
            String title = previewImage.getTitle() != null ? previewImage.getTitle() : "主题预览";
            String description = previewImage.getDescription() != null ? previewImage.getDescription() : "";
            int type = previewImage.getImageType() != null ? previewImage.getImageType() : 0;

            bannerItems.add(new ThemeBannerModel.BannerItem(imageUrl, title, description, type));
        }

        return bannerItems;
    }

    /**
     * 构建完整的图片URL
     */
    private String buildImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return "";
        }
        
        // 如果是相对路径，添加基础URL
        if (!imageUrl.startsWith("http")) {
            return Const.DEV_BASEURL + imageUrl;
        }
        
        return imageUrl;
    }

    /**
     * 创建默认轮播图数据
     */
    private java.util.List<ThemeBannerModel.BannerItem> createDefaultBannerItems() {
        java.util.List<ThemeBannerModel.BannerItem> defaultItems = new java.util.ArrayList<>();
        defaultItems.add(new ThemeBannerModel.BannerItem(
                "", "主题预览", "查看主题整体效果", 0));
        return defaultItems;
    }

    /**
     * 使用轮播图数据设置轮播图
     */
    private void setupBannerWithItems(java.util.List<ThemeBannerModel.BannerItem> bannerItems) {
        currentBannerItemCount = bannerItems.size();

        // 根据图片数量动态配置轮播图
        configureBannerBehavior(currentBannerItemCount);

        // 创建轮播图
        binding.bannerViewPager.create(bannerItems);

        MyLog.d(TAG, String.format("轮播图创建完成，共%d张图片", bannerItems.size()));

        // 启动自动播放（如果是多张图片）
        if (currentBannerItemCount > 1) {
            binding.bannerViewPager.startLoop();
            MyLog.d(TAG, "多张图片，启动自动轮播");
        }
    }

    /**
     * 根据图片数量配置轮播图行为
     */
    private void configureBannerBehavior(int itemCount) {
        try {
            if (itemCount <= 1) {
                // 单张图片：禁用自动播放和循环，隐藏指示器
                binding.bannerViewPager.setAutoPlay(false)
                        .setCanLoop(false)
                        .setIndicatorVisibility(View.GONE);
                MyLog.d(TAG, "单张图片配置：禁用轮播");
            } else {
                // 多张图片：启用自动播放和循环，显示指示器
                binding.bannerViewPager.setAutoPlay(true)
                        .setCanLoop(true)
                        .setInterval(4000)
                        .setIndicatorVisibility(View.VISIBLE);
                MyLog.d(TAG, "多张图片配置：启用轮播");
            }
        } catch (Exception e) {
            MyLog.e(TAG, "配置轮播图行为失败", e);
        }
    }


    /**
     * 构建点击反馈消息
     */
    private String buildClickFeedbackMessage(int position) {
        if (currentBannerItemCount <= 1) {
            return "主题预览图";
        }

        String baseMessage = String.format("第%d张图片，共%d张", position + 1, currentBannerItemCount);
        
        // 根据位置添加额外信息
        if (position == 0) {
            return baseMessage + " - 卡片模式";
        } else if (position == 1) {
            return baseMessage + " - 地图模式";
        }
        
        return baseMessage;
    }

    // ================================ 主题操作 ================================

    /**
     * 应用主题按钮点击处理
     */
    private void onApplyClick() {
        if (!checkClickDebounce()) {
            return;
        }

        MyLog.d(TAG, "点击应用主题: " + themeInfo.getThemeName());

        // 检查主题是否已下载
        if (!isThemeDownloaded()) {
            MToast.makeTextShort("请先下载主题");
            return;
        }

        // 检查是否已经是当前主题
        if (Boolean.TRUE.equals(themeInfo.getIsCurrentTheme())) {
            MToast.makeTextShort("该主题已经在使用中");
            return;
        }

        // 显示确认对话框并应用主题
        showApplyConfirmDialog();
    }

    /**
     * 下载主题按钮点击处理
     */
    private void onDownloadClick() {
        if (!checkClickDebounce() || isDownloading || isOperationInProgress) {
            return;
        }

        MyLog.d(TAG, "点击下载主题: " + themeInfo.getThemeName());

        // 检查网络连接
        if (!NetworkUtils.isNetworkConnected(requireSceneContext())) {
            MToast.makeTextShort("网络连接不可用，请检查网络设置");
            return;
        }

        // 开始下载
        startThemeDownload();
    }

    /**
     * 开始主题下载流程
     */
    private void startThemeDownload() {
        isOperationInProgress = true;
        isDownloading = true;
        updateButtonStates();

        String fileName = getThemeFileName();
        MyLog.d(TAG, "开始下载主题文件: " + (fileName != null ? fileName : "未知"));

        // 更新下载统计
        updateDownloadStats();

        // 开始真实下载
        downloadTimeMillis = String.valueOf(System.currentTimeMillis());
        startRealDownload();
    }

    // ================================ 工具方法 ================================

    /**
     * 检查点击防抖
     */
    private boolean checkClickDebounce() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_TIME) {
            MyLog.d(TAG, "点击过于频繁，忽略本次点击");
            return false;
        }
        lastClickTime = currentTime;
        return true;
    }

    /**
     * 检查主题是否已下载
     */
    private boolean isThemeDownloaded() {
        // 检查数据库标记
        if (Boolean.TRUE.equals(themeInfo.getIsDownloaded())) {
            return true;
        }

        // 检查本地文件是否存在
        String localFilePath = themeInfo.getLocalFilePath();
        if (localFilePath != null && !localFilePath.isEmpty()) {
            return FileUtils.isExist(new File(localFilePath));
        }

        // 检查默认路径（如果有主题包信息）
        if (themeInfo.getThemePackage() != null && themeInfo.getThemePackage().getFileName() != null) {
            String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
            String fileName = themeInfo.getThemePackage().getFileName();
            String localPath = absolutePath + "/skin/" + fileName;
            return FileUtils.isExist(new File(localPath));
        }

        return false;
    }

    /**
     * 获取主题文件名
     */
    private String getThemeFileName() {
        if (themeInfo == null || themeInfo.getThemePackage() == null) {
            return null;
        }
        return themeInfo.getThemePackage().getFileName();
    }

    /**
     * 获取下载链接
     */
    private String getDownloadUrl() {
        if (themeInfo == null || themeInfo.getThemePackage() == null) {
            return null;
        }

        // 优先使用downloadUrl
        String downloadUrl = themeInfo.getThemePackage().getDownloadUrl();
        if (downloadUrl != null && !downloadUrl.isEmpty()) {
            return buildImageUrl(downloadUrl); // 复用URL构建逻辑
        }

        // 回退到packagePath
        String packagePath = themeInfo.getThemePackage().getPackagePath();
        if (packagePath != null && !packagePath.isEmpty()) {
            return buildImageUrl(packagePath);
        }

        return null;
    }

    /**
     * 显示成功消息
     */
    private void showSuccessMessage(String message) {
        MToast.makeTextShort(message);
        MyLog.d(TAG, "操作成功: " + message);
    }

    /**
     * 显示错误消息
     */
    private void showErrorMessage(String message) {
        MToast.makeTextShort(message);
        MyLog.e(TAG, "错误: " + message);
    }

    // ================================ 主题应用逻辑 ================================

    /**
     * 显示应用确认对话框
     */
    private void showApplyConfirmDialog() {
        boolean isCurrentTheme = Boolean.TRUE.equals(themeInfo.getIsCurrentTheme());
        if (isCurrentTheme) {
            MToast.makeTextShort("该主题已经在使用中");
            return;
        }

        String message = buildApplyConfirmMessage();
        
        // TODO: 使用项目统一的确认对话框
        // 这里暂时直接执行，实际项目中应该显示确认对话框
        MyLog.d(TAG, "确认应用主题: " + message);
        applyTheme();
    }

    /**
     * 构建应用确认消息
     */
    private String buildApplyConfirmMessage() {
        String message = "确定要应用主题「" + themeInfo.getThemeName() + "」吗？";

        // 检查是否需要切换模式
        Integer themeType = themeInfo.getThemeType();
        if (themeType != null) {
            boolean isNightMode = SkinManager.getInstance().isNightModeNow();

            // 根据API文档：0=白天，1=夜间
            if ((themeType == 0 && isNightMode) || (themeType == 1 && !isNightMode)) {
                String modeText = themeType == 0 ? "白天模式" : "夜间模式";
                message += "\n\n应用后将自动切换到" + modeText;
            }
        }

        return message;
    }

    /**
     * 应用主题
     */
    private void applyTheme() {
        if (isOperationInProgress) {
            return;
        }

        isOperationInProgress = true;
        updateButtonStates();

        try {
            Integer themeType = themeInfo.getThemeType();
            if (themeType == null) {
                throw new RuntimeException("主题类型未知");
            }

            String fileName = getThemeFileName();
            if (fileName == null) {
                throw new RuntimeException("无法获取主题文件名");
            }

            boolean isNightMode = SkinManager.getInstance().isNightModeNow();

            // 根据主题类型应用主题
            applyThemeByType(themeType, fileName, isNightMode);
            
        } catch (Exception e) {
            MyLog.e(TAG, "应用主题失败", e);
            MToast.makeTextShort("应用主题失败，请重试");
        } finally {
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    /**
     * 根据主题类型应用主题
     */
    private void applyThemeByType(int themeType, String fileName, boolean isNightMode) {
        // 注册并加载皮肤
        registerAndLoadSkin();

        if (themeType == 0) {
            // 白天主题
            SettingsManager.setDayTheme(fileName);
            if (isNightMode) {
                SettingsManager.setThemeMode(1); // 切换到白天模式
            }
            showSuccessMessage("已应用白天主题: " + fileName);
        } else if (themeType == 1) {
            // 夜间主题
            SettingsManager.setNightTheme(fileName);
            if (!isNightMode) {
                SettingsManager.setThemeMode(2); // 切换到夜间模式
            }
            showSuccessMessage("已应用夜间主题: " + fileName);
        }

        // 更新主题使用状态
        updateThemeUsageStatus();
    }

    /**
     * 注册并加载皮肤
     */
    private void registerAndLoadSkin() {
        String fileName = getThemeFileName();
        String localFilePath = themeInfo.getLocalFilePath();

        if (localFilePath != null && !localFilePath.isEmpty() && fileName != null) {
            // 使用本地文件路径
            SkinManager.getInstance().registerFileSkin(localFilePath);
            SkinManager.getInstance().loadSkin(fileName);
        } else if (fileName != null) {
            // 使用默认路径
            String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
            String skinPath = absolutePath + "/skin/" + fileName;
            SkinManager.getInstance().registerFileSkin(skinPath);
            SkinManager.getInstance().loadSkin(fileName);
        } else {
            // 恢复默认主题
            SkinManager.getInstance().restoreDefaultTheme();
        }

        MyLog.v(TAG, "应用主题: " + (fileName != null ? fileName : "默认主题"));
    }

    /**
     * 更新主题使用状态
     */
    private void updateThemeUsageStatus() {
        // 设置主题为当前使用
        disposables.add(
                dbManager.setCurrentTheme(themeInfo.getId())
                        .subscribe(
                                result -> {
                                    MyLog.v(TAG, "主题使用状态更新成功");
                                    updateActionBar();
                                },
                                throwable -> MyLog.e(TAG, "主题使用状态更新失败", throwable)
                        )
        );
    }

    // ================================ 下载统计更新 ================================

    /**
     * 更新下载统计
     */
    private void updateDownloadStats() {
        MyLog.d(TAG, "更新下载统计: " + themeInfo.getThemeName());
        updateDownloadStatsNewAPI();
    }

    /**
     * 使用新API更新下载统计
     */
    private void updateDownloadStatsNewAPI() {
        String deviceId = android.provider.Settings.Secure.getString(
                requireSceneContext().getContentResolver(),
                android.provider.Settings.Secure.ANDROID_ID);

        String requestBody = String.format(
                "{\n" +
                "  \"packageId\": null,\n" +
                "  \"userId\": null,\n" +
                "  \"deviceId\": \"%s\",\n" +
                "  \"appVersion\": \"%s\",\n" +
                "  \"platform\": \"android\",\n" +
                "  \"downloadSource\": \"theme_detail\"\n" +
                "}",
                deviceId,
                com.smartcar.easylauncher.BuildConfig.VERSION_NAME
        );

        disposables.add(
                RxHttp.postJson(Const.NEW_THEME_DOWNLOAD + themeInfo.getId())
                        .addAll(requestBody)
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleDownloadStatsSuccess,
                                this::handleDownloadStatsError
                        )
        );
    }

    /**
     * 处理下载统计更新成功
     */
    private void handleDownloadStatsSuccess(String responseString) {
        try {
            Gson gson = new Gson();
            TypeToken<ApiResponse<Object>> typeToken = new TypeToken<ApiResponse<Object>>() {};
            ApiResponse<Object> response = gson.fromJson(responseString, typeToken.getType());

            if (response != null && response.isSuccess()) {
                MyLog.v(TAG, "下载统计更新成功: " + response.getMsg());
                
                // 更新本地显示的下载量
                Long currentCount = themeInfo.getDownloadCount() != null ? themeInfo.getDownloadCount() : 0L;
                themeInfo.setDownloadCount(currentCount + 1);
                
                // 更新UI显示
                binding.tvDownloadCount.setText("下载量: " + themeInfo.getDownloadCount());
            } else {
                MyLog.w(TAG, "下载统计响应异常: " + (response != null ? response.getMsg() : "null"));
            }
        } catch (Exception e) {
            MyLog.e(TAG, "解析下载统计响应失败", e);
        }
    }

    /**
     * 处理下载统计更新失败
     */
    private void handleDownloadStatsError(Throwable throwable) {
        MyLog.e(TAG, "下载统计更新失败", throwable);
        // 静默失败，不影响下载流程
    }

    // ================================ 操作栏管理 ================================

    /**
     * 更新操作栏状态
     * 根据主题当前状态显示相应的操作按钮
     */
    private void updateActionBar() {
        if (themeInfo == null || actionBar == null) {
            updateActionBarToLoading();
            return;
        }

        boolean isCurrentTheme = Boolean.TRUE.equals(themeInfo.getIsCurrentTheme());
        boolean isDownloaded = Boolean.TRUE.equals(themeInfo.getIsDownloaded());

        MyLog.d(TAG, String.format("更新操作栏状态 - 已下载: %s, 当前使用: %s", 
            isDownloaded, isCurrentTheme));

        if (isDownloaded) {
            // 已下载的主题：显示应用按钮
            updateActionBarForDownloadedTheme(isCurrentTheme);
        } else {
            // 未下载的主题：显示下载和应用按钮
            updateActionBarForUndownloadedTheme();
        }
    }

    /**
     * 为已下载主题更新操作栏
     */
    private void updateActionBarForDownloadedTheme(boolean isCurrentTheme) {
        if (isCurrentTheme) {
            actionBar.setPrimaryAction("当前使用", () -> {
                // 当前主题状态下禁用点击
                MToast.makeTextShort("该主题已经在使用中");
            });
            actionBar.setPrimaryActionEnabled(false);
        } else {
            actionBar.setPrimaryAction("应用主题", this::onApplyClick);
            actionBar.setPrimaryActionEnabled(!isOperationInProgress);
        }
        actionBar.hideSecondaryAction();
    }

    /**
     * 为未下载主题更新操作栏
     */
    private void updateActionBarForUndownloadedTheme() {
        // 主要操作：应用主题（需要先下载）
        actionBar.setPrimaryAction("应用主题", this::onApplyClick);
        actionBar.setPrimaryActionEnabled(false); // 未下载时不能直接应用

        // 次要操作：下载主题
        actionBar.setSecondaryAction("下载主题", this::onDownloadClick);
        actionBar.setSecondaryActionEnabled(!isDownloading && !isOperationInProgress);
    }

    /**
     * 更新操作按钮状态
     * 用于下载过程中的状态更新
     */
    private void updateButtonStates() {
        if (actionBar == null) return;

        boolean isCurrentTheme = Boolean.TRUE.equals(themeInfo.getIsCurrentTheme());
        
        // 根据操作状态禁用/启用按钮
        actionBar.setPrimaryActionEnabled(!isOperationInProgress && !isCurrentTheme);
        actionBar.setSecondaryActionEnabled(!isOperationInProgress && !isDownloading);

        // 更新进度显示
        if (isOperationInProgress && isDownloading) {
            actionBar.showProgress(0, "准备下载...");
        } else if (!isOperationInProgress) {
            actionBar.hideProgress();
            updateActionBar(); // 恢复正常状态
        }
    }


    /**
     * 开始真实下载
     */
    private void startRealDownload() {
        String fileName = getThemeFileName();
        String downloadUrl = getDownloadUrl();

        if (fileName == null || downloadUrl == null) {
            onDownloadError(new RuntimeException("无法获取主题文件信息"));
            return;
        }

        MyLog.d(TAG, "开始下载主题文件:");
        MyLog.d(TAG, "  - 文件名: " + fileName);
        MyLog.d(TAG, "  - 下载URL: " + downloadUrl);

        // 文件存储路径
        String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
        String destPath = absolutePath + "/skin/" + fileName;

        MyLog.d(TAG, "下载路径: " + destPath);

        downloadDisposable = RxHttp.get(downloadUrl)
                .tag(downloadTimeMillis)
                .toDownloadObservable(destPath)
                .onMainProgress(progress -> {
                    // 下载进度回调
                    int currentProgress = progress.getProgress(); // 当前进度 0-100
                    long currentSize = progress.getCurrentSize(); // 当前已下载的字节大小
                    long totalSize = progress.getTotalSize(); // 要下载的总字节大小

                    MyLog.v(TAG, "下载进度: " + currentProgress + "%");

                    // 更新进度显示
                    if (actionBar != null) {
                        actionBar.showProgress(currentProgress, "下载中...");
                    }
                })
                .subscribe(
                        downloadPath -> {
                            // 下载完成
                            MyLog.d(TAG, "下载完成: " + downloadPath);
                            onRealDownloadComplete(downloadPath);
                        },
                        throwable -> {
                            // 下载失败
                            MyLog.e(TAG, "下载失败", throwable);
                            onDownloadError(throwable);
                        }
                );

        // 添加到disposables中管理
        disposables.add(downloadDisposable);
    }


    /**
     * 真实下载完成处理
     */
    private void onRealDownloadComplete(String downloadPath) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        // 更新主题下载状态
        themeInfo.setIsDownloaded(true);
        themeInfo.setLocalFilePath(downloadPath);
        themeInfo.setDownloadTime(String.valueOf(System.currentTimeMillis()));

        // 确保所有必需字段都有值，避免数据库插入时的NullPointerException
        ensureRequiredFields(themeInfo);

        // 打印详细的主题信息用于调试
        MyLog.d(TAG, "准备保存主题到数据库:");
        MyLog.d(TAG, "  - ID: " + themeInfo.getId());
        MyLog.d(TAG, "  - Name: " + themeInfo.getThemeName());
        MyLog.d(TAG, "  - Author: " + themeInfo.getAuthor());
        MyLog.d(TAG, "  - ThemeType: " + themeInfo.getThemeType());
        MyLog.d(TAG, "  - DownloadCount: " + themeInfo.getDownloadCount());
        MyLog.d(TAG, "  - IsDownloaded: " + themeInfo.getIsDownloaded());
        MyLog.d(TAG, "  - IsCurrentTheme: " + themeInfo.getIsCurrentTheme());
        MyLog.d(TAG, "  - LocalFilePath: " + themeInfo.getLocalFilePath());
        MyLog.d(TAG, "  - DownloadTime: " + themeInfo.getDownloadTime());
        MyLog.d(TAG, "  - ThemePackage: " + (themeInfo.getThemePackage() != null ? "存在" : "null"));

        // 保存主题到新数据库 - 使用简化的保存方法
        disposables.add(
                dbManager.saveDownloadedTheme(themeInfo)
                        .subscribe(
                                result -> {
                                    MyLog.v(TAG, "主题保存到数据库成功，ID: " + result);
                                    showSuccessMessage("主题下载完成，可在「我的主题」中查看");
                                    updateActionBar();
                                },
                                throwable -> {
                                    MyLog.e(TAG, "简化保存方法失败，尝试基本保存方法", throwable);
                                    // 尝试备用的基本保存方法
                                    tryBasicSave(themeInfo);
                                }
                        )
        );
    }

    /**
     * 确保所有必需字段都有值
     */
    private void ensureRequiredFields(UnifiedThemeModel theme) {
        // 确保ID不为空
        if (theme.getId() == null) {
            MyLog.e(TAG, "主题ID为空，无法保存");
            return;
        }

        // 确保主题名称不为空
        if (theme.getThemeName() == null || theme.getThemeName().trim().isEmpty()) {
            theme.setThemeName("主题_" + theme.getId());
            MyLog.d(TAG, "设置默认主题名称: " + theme.getThemeName());
        }

        // 确保作者不为空
        if (theme.getAuthor() == null || theme.getAuthor().trim().isEmpty()) {
            theme.setAuthor("未知作者");
            MyLog.d(TAG, "设置默认作者: " + theme.getAuthor());
        }

        // 确保主题类型不为空
        if (theme.getThemeType() == null) {
            theme.setThemeType(0); // 默认为夜间主题
            MyLog.d(TAG, "设置默认主题类型: " + theme.getThemeType());
        }

        // 确保下载状态不为空
        if (theme.getIsDownloaded() == null) {
            theme.setIsDownloaded(true);
            MyLog.d(TAG, "设置下载状态: " + theme.getIsDownloaded());
        }

        // 确保当前主题状态不为空
        if (theme.getIsCurrentTheme() == null) {
            theme.setIsCurrentTheme(false);
            MyLog.d(TAG, "设置当前主题状态: " + theme.getIsCurrentTheme());
        }

        // 确保分类不为空（数据库中被标记为NOT NULL）
        if (theme.getClassify() == null) {
            theme.setClassify(1); // 默认为用户主题
            MyLog.d(TAG, "设置默认分类: " + theme.getClassify());
        }

        // 确保使用状态不为空（数据库中被标记为NOT NULL）
        if (theme.isUse() == null) {
            theme.setUse(false);
            MyLog.d(TAG, "设置使用状态: " + theme.isUse());
        }

        // 确保下载时间不为空（如果已下载）
        if (theme.getIsDownloaded() != null && theme.getIsDownloaded() && 
            (theme.getDownloadTime() == null || theme.getDownloadTime().trim().isEmpty())) {
            theme.setDownloadTime(String.valueOf(System.currentTimeMillis()));
            MyLog.d(TAG, "设置下载时间: " + theme.getDownloadTime());
        }

        // 确保本地文件路径不为空（如果已下载）
        if (theme.getIsDownloaded() != null && theme.getIsDownloaded() && 
            (theme.getLocalFilePath() == null || theme.getLocalFilePath().trim().isEmpty())) {
            // 如果没有本地文件路径，尝试从主题包信息构建
            if (theme.getThemePackage() != null && theme.getThemePackage().getFileName() != null) {
                String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
                String localPath = absolutePath + "/skin/" + theme.getThemePackage().getFileName();
                theme.setLocalFilePath(localPath);
                MyLog.d(TAG, "设置本地文件路径: " + theme.getLocalFilePath());
            }
        }

        // 确保updateTime不为空（数据库操作时可能被当作非空字段）
        if (theme.getUpdateTime() == null || theme.getUpdateTime().trim().isEmpty()) {
            theme.setUpdateTime(String.valueOf(System.currentTimeMillis()));
            MyLog.d(TAG, "设置更新时间: " + theme.getUpdateTime());
        }

        // 确保createTime不为空（如果为空）
        if (theme.getCreateTime() == null || theme.getCreateTime().trim().isEmpty()) {
            theme.setCreateTime(String.valueOf(System.currentTimeMillis()));
            MyLog.d(TAG, "设置创建时间: " + theme.getCreateTime());
        }

        MyLog.d(TAG, "必需字段检查完成");
    }

    /**
     * 尝试基本保存方法 - 备用方案
     */
    private void tryBasicSave(UnifiedThemeModel themeInfo) {
        MyLog.d(TAG, "尝试使用基本保存方法");

        // 确保所有必需字段都有值
        ensureRequiredFields(themeInfo);

        disposables.add(
                dbManager.saveThemeBasic(themeInfo)
                        .subscribe(
                                result -> {
                                    MyLog.v(TAG, "基本保存方法成功，ID: " + result);
                                    showSuccessMessage("主题下载完成，可在「我的主题」中查看");
                                    updateActionBar();
                                },
                                throwable -> {
                                    MyLog.e(TAG, "所有保存方法都失败", throwable);

                                    String errorMsg = "主题下载完成，但保存失败。数据库正在重建，请重启应用后重试。";
                                    showErrorMessage(errorMsg);
                                    updateActionBar();
                                }
                        )
        );
    }

    /**
     * 下载错误处理
     */
    private void onDownloadError(Throwable throwable) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        String errorMessage = "下载失败";
        if (throwable != null) {
            errorMessage += ": " + throwable.getMessage();
        }

        MyLog.e(TAG, errorMessage, throwable);
        showErrorMessage(errorMessage);

        // 更新操作栏状态
        updateActionBar();
    }


    @Override
    public void onResume() {
        super.onResume();

        // 恢复轮播图自动播放
        if (currentBannerItemCount > 1) {
            binding.bannerViewPager.startLoop();
            MyLog.d(TAG, "页面恢复，自动轮播已启动");
        }

        // 重置操作状态（防止异常情况下状态未正确恢复）
        if (isOperationInProgress && !isDownloading) {
            MyLog.w(TAG, "检测到异常操作状态，重置状态");
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        // 暂停轮播图自动播放
        binding.bannerViewPager.stopLoop();
        MyLog.d(TAG, "页面暂停，自动轮播已停止");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 取消下载
        if (downloadDisposable != null && !downloadDisposable.isDisposed()) {
            downloadDisposable.dispose();
        }

        // 停止轮播图
        binding.bannerViewPager.stopLoop();

        // 清理资源
        disposables.clear();
        bannerAdapter = null;
        actionBar = null;
        binding = null;
    }


}
