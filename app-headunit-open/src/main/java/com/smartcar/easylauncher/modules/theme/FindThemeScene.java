package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.bytedance.scene.animation.SharedElementSceneTransitionExecutor;
import com.bytedance.scene.animation.interaction.scenetransition.AutoSceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.SceneTransition;
import com.bytedance.scene.animation.interaction.scenetransition.visiblity.Fade;
import com.bytedance.scene.interfaces.PushOptions;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.layoutmanager.QuickGridLayoutManager;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.theme.api.ThemeListModel;
import com.smartcar.easylauncher.shared.adapter.theme.NewFindThemeAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.databinding.SceneAllThemeBinding;
import com.smartcar.easylauncher.data.model.theme.api.TableDataInfo;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.google.gson.Gson;
import com.smartcar.easylauncher.shared.utils.ui.ScreenUtils;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import rxhttp.RxHttp;
import rxhttp.wrapper.cache.CacheMode;

/**
 * 推荐主题Scene - 优化版本
 * 展示所有可用的推荐主题，支持主题预览、下载和应用
 * 优化内容：
 * 1. 使用BaseQuickAdapter4的空视图功能
 * 2. 优化命名规范，提高代码可读性
 * 3. 减少代码量，提升性能
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class FindThemeScene extends BaseScene {

    private static final String TAG = "FindThemeScene";

    private SceneAllThemeBinding mBinding;
    private NewFindThemeAdapter mThemeAdapter;
    private final List<ThemeListModel.RowsDTO> mThemeDataList = new ArrayList<>();
    private final CompositeDisposable mDisposables = new CompositeDisposable();

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        mBinding = SceneAllThemeBinding.inflate(inflater, container, false);
        return mBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setupViews();
        loadThemeDataFromNetwork();
    }

    /**
     * 设置视图组件 - 优化版本
     */
    private void setupViews() {
        setupRecyclerView();
        setupAdapter();
        setupSwipeRefresh();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        int spanCount = calculateOptimalSpanCount();
        QuickGridLayoutManager layoutManager = new QuickGridLayoutManager(requireActivity(), spanCount);
        mBinding.rvTheme.setLayoutManager(layoutManager);

        // RecyclerView性能优化配置
        mBinding.rvTheme.setHasFixedSize(true);
        mBinding.rvTheme.setItemViewCacheSize(20);
    }

    /**
     * 设置适配器和空视图
     */
    private void setupAdapter() {
        mThemeAdapter = new NewFindThemeAdapter();
        mThemeAdapter.setOnItemClickListener(this::handleThemeItemClick);

        // 初始状态禁用空视图，避免在加载时显示
        mThemeAdapter.setStateViewEnable(false);
        mThemeAdapter.setUseStateViewSize(true);
        View notDataView = LayoutInflater.from(requireActivity()).inflate(R.layout.theme_empty_view, mBinding.rvTheme, false);

        mThemeAdapter.setStateView(notDataView);

        mBinding.rvTheme.setAdapter(mThemeAdapter);
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        mBinding.swipeRefreshLayout.setOnRefreshListener(this::refreshThemeData);
        mBinding.swipeRefreshLayout.setColorSchemeResources(
                R.color.brand_primary,
                R.color.main_button_confirm_center_color,
                R.color.main_button_confirm_end_color
        );
    }


    /**
     * 从网络加载主题数据 - 使用新的主题API
     */
    @SuppressLint("CheckResult")
    private void loadThemeDataFromNetwork() {
        setLoadingState(true);

        MyLog.v(TAG, "开始请求主题数据 - 使用新API");

        mDisposables.add(
                RxHttp.get(Const.NEW_THEME_LIST)
                        .setCacheMode(CacheMode.ONLY_NETWORK)
                        .add("pageNum", "1")
                        .add("pageSize", "50")
                        .add("status", "1")  // 只获取发布状态的主题
                        .add("releaseStatus", "2")  // 只获取正式发布的主题
                        .add("sortBy", "heat")
                        .add("sortOrder", "1")  // 1=降序，0=升序
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleNewThemeDataSuccessString,
                                this::handleThemeDataError
                        )
        );
    }

    /**
     * 刷新主题数据
     */
    private void refreshThemeData() {
        loadThemeDataFromNetwork();
    }


    /**
     * 处理新API主题数据加载成功 - 字符串响应版本
     */
    private void handleNewThemeDataSuccessString(String responseString) {
        setLoadingState(false);

        MyLog.v(TAG, "新API主题数据响应: " + responseString);

        try {
            Gson gson = new Gson();
            ThemeListModel newThemeInfo = gson.fromJson(responseString, ThemeListModel.class);

            if (newThemeInfo != null &&
                    newThemeInfo.getRows() != null && !newThemeInfo.getRows().isEmpty()) {

                MyLog.v(TAG, "新API主题数据解析成功，数量: " + newThemeInfo.getRows().size());

                // 直接使用新API数据
                mThemeDataList.clear();
                mThemeDataList.addAll(newThemeInfo.getRows());
                updateAdapterData();
            } else {
                MyLog.w(TAG, "新API主题数据为空或解析失败");
                showEmptyState();
            }
        } catch (Exception e) {
            MyLog.e(TAG, "解析新API主题数据失败", e);
            showEmptyState();
        }
    }


    /**
     * 处理主题数据加载失败
     */
    private void handleThemeDataError(Throwable throwable) {
        setLoadingState(false);
        MyLog.e(TAG, "主题数据加载失败", throwable);
        MToast.makeTextShort("主题数据加载失败，请检查网络连接");
        // 加载失败时显示空视图
        showEmptyState();
    }


    /**
     * 更新适配器数据
     */
    private void updateAdapterData() {
        // 有数据时禁用空视图，确保正常显示数据
        mThemeAdapter.setStateViewEnable(false);
        mThemeAdapter.submitList(new ArrayList<>(mThemeDataList));
    }

    /**
     * 显示空状态
     */
    private void showEmptyState() {
        // 启用空视图并清空数据列表
        mThemeAdapter.setStateViewEnable(true);
        mThemeAdapter.submitList(new ArrayList<>());
    }

    /**
     * 处理主题项点击事件
     */
    private void handleThemeItemClick(BaseQuickAdapter<?, ?> adapter, View view, int position) {
        if (isValidPosition(position)) {
            ThemeListModel.RowsDTO themeInfo = mThemeDataList.get(position);
            navigateToThemeDetail(themeInfo);
        }
    }

    /**
     * 检查位置是否有效
     */
    private boolean isValidPosition(int position) {
        return position >= 0 && position < mThemeDataList.size();
    }

    /**
     * 导航到主题详情页面
     */
    private void navigateToThemeDetail(ThemeListModel.RowsDTO rowsDTO) {
        // 创建共享元素动画映射
        ArrayMap<String, SceneTransition> transitionMap = new ArrayMap<>();
        transitionMap.put(ThemeDetailScene.VIEW_NAME_THEME_IMAGE + rowsDTO.getId(), new AutoSceneTransition());
        transitionMap.put(ThemeDetailScene.VIEW_NAME_THEME_TITLE + rowsDTO.getId(), new AutoSceneTransition());

        // 配置动画执行器
        SharedElementSceneTransitionExecutor transitionExecutor =
                new SharedElementSceneTransitionExecutor(transitionMap, new Fade());

        // 创建推送选项
        PushOptions pushOptions = new PushOptions.Builder()
                .setAnimation(transitionExecutor)
                .build();

        // 导航到详情页面 - 使用新的构造方法
        MyLog.d(TAG, "导航到主题详情: " + rowsDTO.getThemeName());
        ThemeDetailScene detailScene = ThemeDetailScene.newInstance(rowsDTO);
        getNavigationScene(this).push(detailScene, pushOptions);
    }

    /**
     * 设置加载状态
     */
    private void setLoadingState(boolean isLoading) {
        if (mBinding != null) {
            mBinding.swipeRefreshLayout.setRefreshing(isLoading);
        }
    }

    /**
     * 计算最优列数 - 简化版本
     * 带鱼屏4列，横屏3列，竖屏2列
     */
    private int calculateOptimalSpanCount() {
        int screenWidth = ScreenUtils.getWindowWidth(requireActivity());
        int screenHeight = ScreenUtils.getWindowHeigh(requireActivity());
        int orientation = ScreenUtils.getScreenOrientation(screenWidth, screenHeight);

        switch (orientation) {
            case ScreenUtils.ORIENTATION_PORTRAIT:
                MyLog.d(TAG, "竖屏模式，使用2列布局");
                return 2;
            case ScreenUtils.ORIENTATION_LANDSCAPE:
                MyLog.d(TAG, "横屏模式，使用3列布局");
                return 3;
            case ScreenUtils.ORIENTATION_LONG_LANDSCAPE:
                MyLog.d(TAG, "带鱼屏模式，使用3列布局");
                return 3;
            default:
                MyLog.d(TAG, "未知屏幕方向，使用默认3列布局");
                return 3;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        mDisposables.clear();
        mBinding = null;
    }
}
