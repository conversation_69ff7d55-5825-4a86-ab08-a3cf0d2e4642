package com.smartcar.easylauncher.shared.adapter.theme;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;

import com.bumptech.glide.Glide;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.modules.theme.LocalThemeDetailScene;
import com.smartcar.easylauncher.shared.view.LabelView;


/**
 * 本地主题适配器 - 优化版本
 * 支持默认主题图片显示和现代化UI设计
 * 只显示必要的标签：更新标签和正在使用标签
 *
 * <AUTHOR>
 * <AUTHOR> (优化)
 */
public class SceneLocalThemeAdapter extends BaseQuickAdapter<UnifiedThemeModel, QuickViewHolder> {

    /**
     * 构造函数
     */
    public SceneLocalThemeAdapter() {
        super();
    }

    /**
     * 更新标签背景颜色
     *
     * @param view  标签视图
     * @param label 标签文本
     */
    private void updateBackground(LabelView view, String label) {
        if (label == null) {
            return;
        }
        int color;
        switch (label) {
            case "热门":
                color = ContextCompat.getColor(getContext(), R.color.colorHot);
                break;
            case "最新":
                color = ContextCompat.getColor(getContext(), R.color.colorNew);
                break;
            case "精选":
                color = ContextCompat.getColor(getContext(), R.color.colorChoice);
                break;
            case "推荐":
                color = ContextCompat.getColor(getContext(), R.color.colorRecommend);
                break;
            case "更新":
                color = ContextCompat.getColor(getContext(), R.color.colorUpdate);
                break;
            case "限免":
                color = ContextCompat.getColor(getContext(), R.color.colorFree);
                break;
            case "节日":
                color = ContextCompat.getColor(getContext(), R.color.colorFestival);
                break;
            default:
                color = ContextCompat.getColor(getContext(), R.color.colorDefault);
                break;
        }
        view.setBgColor(color);
    }


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable UnifiedThemeModel item) {
        assert item != null;

        // 显示更新标签 - 参考老逻辑的思路
        LabelView updateLabel = helper.getView(R.id.lv_heat);
        if (Boolean.TRUE.equals(item.getHasUpdate())) {
            updateLabel.setVisibility(View.VISIBLE);
            updateLabel.setText("更新");
            updateBackground(updateLabel, "更新");
        } else {
            updateLabel.setVisibility(View.GONE);
        }

        helper.setText(R.id.grid_item_app_name, item.getThemeName());

        // 优化下载量显示 - 更简洁的现代化设计
        if (item.getClassify() != null && item.getClassify() == 0) {
            // 默认主题不显示下载量
            helper.getView(R.id.grid_item_number).setVisibility(View.GONE);
        } else {
            helper.getView(R.id.grid_item_number).setVisibility(View.VISIBLE);
            helper.setText(R.id.grid_item_number, formatDownloadCount(item.getDownloadCount()));
        }

        // 安全地解析和加载主题图片
        loadThemeImage(item, (ImageView) helper.getView(R.id.grid_item_app_icon));

        // 显示使用状态和主题类型标签
        LabelView defaultLabel = helper.getView(R.id.lv_default);
        if ((item.isUse() != null && item.isUse()) || (item.getIsCurrentTheme() != null && item.getIsCurrentTheme())) {
            defaultLabel.setVisibility(View.VISIBLE);
            if (item.getThemeType() == 0) {
                defaultLabel.setText("夜间");
                defaultLabel.setBgColor(ContextCompat.getColor(getContext(), R.color.main_button_confirm_center_color));
            } else if (item.getThemeType() == 1) {
                defaultLabel.setText("白天");
                defaultLabel.setBgColor(ContextCompat.getColor(getContext(), R.color.brand_primary));
            }
        } else {
            defaultLabel.setVisibility(View.GONE);
        }

        // 设置共享元素动画的transitionName
        ViewCompat.setTransitionName(helper.getView(R.id.grid_item_app_icon),
                LocalThemeDetailScene.VIEW_NAME_THEME_IMAGE + item.getId());
        ViewCompat.setTransitionName(helper.getView(R.id.grid_item_app_name),
                LocalThemeDetailScene.VIEW_NAME_THEME_TITLE + item.getId());

    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.find_theme_item, viewGroup);
    }

    /**
     * 安全地加载主题图片 - 优化版本
     * 支持默认主题图片资源和网络图片
     *
     * @param item      主题信息
     * @param imageView 目标ImageView
     */
    private void loadThemeImage(UnifiedThemeModel item, ImageView imageView) {
        try {
            // 检查图片数据是否为空
            String imgData = item.getCoverImage();
            if (imgData == null || imgData.trim().isEmpty()) {
                // 使用默认占位图
                loadDefaultThemeImage(imageView);
                return;
            }

            // 检查是否为默认主题的资源ID
            if (item.getClassify() != null && item.getClassify() == 0) {
                // 默认主题，使用本地资源图片
                try {
                    int resourceId = Integer.parseInt(imgData);
                    loadDefaultThemeImageResource(imageView, resourceId);
                    return;
                } catch (NumberFormatException e) {
                    // 不是资源ID，使用默认图片
                    loadDefaultThemeImage(imageView);
                    return;
                }
            }

            // 用户下载的主题，直接使用网络图片URL（参考NewFindThemeAdapter的简化逻辑）
            // 不再解析复杂的JSON，直接使用coverImage作为图片URL
            Glide.with(getContext())
                    .load(imgData)
                    .centerCrop() // 居中裁剪，保持宽高比
                    .thumbnail(0.1f) // 先加载缩略图提升体验
                    .into(imageView);

        } catch (Exception e) {
            // 任何异常都使用默认图片
            e.printStackTrace();
            loadDefaultThemeImage(imageView);
        }
    }

    /**
     * 加载默认主题图片资源
     * @param imageView 目标ImageView
     * @param resourceId 资源ID
     */
    private void loadDefaultThemeImageResource(ImageView imageView, int resourceId) {
        Glide.with(getContext())
                .load(resourceId)
                .centerCrop()
                .into(imageView);
    }

    /**
     * 加载默认主题图片
     * @param imageView 目标ImageView
     */
    private void loadDefaultThemeImage(ImageView imageView) {
        Glide.with(getContext())
                .load(R.drawable.sl_page_l3c_bg)
                .into(imageView);
    }


    /**
     * 格式化下载量显示 - 现代化简洁设计
     *
     * @param number 下载量数字
     * @return 格式化后的字符串
     */
    private String formatDownloadCount(Long number) {
        if (number == null) {
            return "0";
        }

        long count = number;
        if (count >= 10000) {
            return String.format("%.1fw", count / 10000.0f);
        } else if (count >= 1000) {
            return String.format("%.1fk", count / 1000.0f);
        } else {
            return String.valueOf(count);
        }
    }
}
