<?xml version="1.0" encoding="utf-8"?>
<com.smartcar.easylauncher.shared.view.easylayout.EasyConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/second_sheets_bg_color"
    app:layout_optimizationLevel="standard">

    <!-- 主要内容区域 - 优化滚动性能 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fadeScrollbars="true"
        android:fillViewport="false"
        android:overScrollMode="never"
        android:scrollbarFadeDuration="300"
        android:scrollbars="none"
        android:scrollingCache="true"
        android:smoothScrollbar="true"
        app:layout_constraintBottom_toTopOf="@id/action_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 主内容容器 - 极致优化的卡片设计 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_content_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="12dp"
            android:background="@drawable/sl_page_l1c_bg"
            app:layout_optimizationLevel="standard">

            <!-- 使用Guideline创建左右分割 -->
            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline_vertical_50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <!-- 左侧轮播图 - 减少层级，直接使用 -->
            <com.zhpan.bannerview.BannerViewPager
                android:id="@+id/banner_view_pager"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_margin="16dp"
                android:layout_marginEnd="8dp"
                android:layerType="hardware"
                app:layout_constraintDimensionRatio="16:9"
                app:layout_constraintEnd_toStartOf="@id/guideline_vertical_50"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- 右侧主题名称 - 优化字体效果 -->
            <TextView
                android:id="@+id/tv_theme_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="10dp"
                android:lineSpacingExtra="3dp"
                android:maxLines="2"
                android:textColor="@color/field_name_color"
                android:textSize="26dp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/guideline_vertical_50"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="炫酷夜间主题" />

            <!-- 主题标签 - 减少层级，直接布局 -->
            <TextView
                android:id="@+id/tv_theme_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/beautiful_label_bg"
                android:fontFamily="sans-serif-medium"
                android:paddingHorizontal="14dp"
                android:paddingVertical="7dp"
                android:textColor="@color/white"
                android:textSize="12dp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintEnd_toStartOf="@id/tv_update_hint"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@id/guideline_vertical_50"
                app:layout_constraintTop_toBottomOf="@id/tv_theme_name"
                tools:text="热门"
                tools:visibility="visible" />

            <!-- 更新提示 - 减少层级，直接布局 -->
            <TextView
                android:id="@+id/tv_update_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="16dp"
                android:layout_marginStart="10dp"
                android:background="@drawable/beautiful_label_bg_orange"
                android:fontFamily="sans-serif-medium"
                android:paddingHorizontal="14dp"
                android:paddingVertical="7dp"
                android:textColor="@color/white"
                android:textSize="12dp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="@id/tv_theme_label"
                app:layout_constraintTop_toBottomOf="@id/tv_theme_name"
                tools:text="有新版本"
                tools:visibility="visible" />

            <!-- 主题描述 - 优化排版 -->
            <TextView
                android:id="@+id/tv_theme_description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="16dp"
                android:ellipsize="end"
                android:maxLines="3"
                android:textColor="@color/field_content_color"
                android:textSize="15dp"
                app:layout_constraintBottom_toBottomOf="@id/banner_view_pager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/guideline_vertical_50"
                app:layout_constraintTop_toBottomOf="@id/tv_theme_label"
                app:layout_constraintVertical_bias="0.0"
                tools:text="这是一个非常炫酷的夜间主题，采用深色调设计，为您的夜间使用提供更好的视觉体验。" />


            <!-- 详细信息网格容器 - 极致优化设计 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/info_grid_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/sl_page_l2c_bg"
                android:padding="18dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/banner_view_pager">

                <!-- 使用Guideline创建4等分横向布局 -->
                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_25"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.25" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_50"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_75"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.75" />

                <!-- 作者信息 - 第一列，优化设计 -->
                <ImageView
                    android:id="@+id/ic_author"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_marginBottom="6dp"
                    android:src="@drawable/ic_author"
                    app:layout_constraintBottom_toTopOf="@id/tv_theme_author"
                    app:layout_constraintEnd_toStartOf="@id/guideline_25"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:tint="@color/brand_primary" />

                <TextView
                    android:id="@+id/tv_theme_author"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="6dp"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textColor="@color/field_content_color"
                    android:textSize="13dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/guideline_25"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ic_author"
                    tools:text="设计师小王" />

                <!-- 类型信息 - 第二列，优化设计 -->
                <ImageView
                    android:id="@+id/ic_type"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_marginBottom="6dp"
                    android:src="@drawable/ic_theme_type"
                    app:layout_constraintBottom_toTopOf="@id/tv_theme_type"
                    app:layout_constraintEnd_toStartOf="@id/guideline_50"
                    app:layout_constraintStart_toEndOf="@id/guideline_25"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:tint="@color/brand_primary" />

                <TextView
                    android:id="@+id/tv_theme_type"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="6dp"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textColor="@color/field_content_color"
                    android:textSize="13dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/guideline_50"
                    app:layout_constraintStart_toEndOf="@id/guideline_25"
                    app:layout_constraintTop_toBottomOf="@id/ic_type"
                    tools:text="夜间主题" />

                <!-- 版本信息 - 第三列，优化设计 -->
                <ImageView
                    android:id="@+id/ic_version"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_marginBottom="6dp"
                    android:src="@drawable/ic_theme_usage"
                    app:layout_constraintBottom_toTopOf="@id/tv_theme_version"
                    app:layout_constraintEnd_toStartOf="@id/guideline_75"
                    app:layout_constraintStart_toEndOf="@id/guideline_50"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:tint="@color/brand_primary" />

                <TextView
                    android:id="@+id/tv_theme_version"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="6dp"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textColor="@color/field_content_color"
                    android:textSize="13dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/guideline_75"
                    app:layout_constraintStart_toEndOf="@id/guideline_50"
                    app:layout_constraintTop_toBottomOf="@id/ic_version"
                    tools:text="版本 1.2.0" />

                <!-- 下载量信息 - 第四列，优化设计 -->
                <ImageView
                    android:id="@+id/ic_download"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_marginBottom="6dp"
                    android:src="@drawable/ic_theme_download"
                    app:layout_constraintBottom_toTopOf="@id/tv_download_count"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/guideline_75"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:tint="@color/brand_primary" />

                <TextView
                    android:id="@+id/tv_download_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="6dp"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textColor="@color/field_content_color"
                    android:textSize="13dp"

                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/guideline_75"
                    app:layout_constraintTop_toBottomOf="@id/ic_download"
                    tools:text="1234次下载" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 更新信息区域 - 极致优化设计 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/update_info_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/sl_page_l2c_bg"
                android:padding="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/info_grid_container"
                tools:visibility="visible">

                <!-- 头部背景区域 - 提供视觉背景 -->
                <View
                    android:id="@+id/update_header_bg"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/sl_page_l3c_bg"
                    app:layout_constraintBottom_toBottomOf="@id/divider_update_header"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 更新图标 - 直接使用ConstraintLayout布局，减少层级 -->
                <ImageView
                    android:id="@+id/iv_update_icon"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginStart="18dp"
                    android:layout_marginTop="18dp"
                    android:background="@drawable/beautiful_label_bg_green"
                    android:padding="8dp"
                    android:src="@drawable/ic_theme_usage"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/white" />

                <!-- 更新标题 - 直接约束到图标右侧 -->
                <TextView
                    android:id="@+id/tv_update_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="8dp"
                    android:fontFamily="sans-serif-medium"
                    android:text="版本信息"
                    android:textColor="@color/field_name_color"
                    android:textSize="17dp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@id/tv_update_badge"
                    app:layout_constraintStart_toEndOf="@id/iv_update_icon"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 版本信息 - 约束到标题下方 -->
                <TextView
                    android:id="@+id/tv_update_version"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginEnd="8dp"
                    android:fontFamily="sans-serif"
                    android:textColor="@color/field_remarks_color"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toStartOf="@id/tv_update_badge"
                    app:layout_constraintStart_toEndOf="@id/iv_update_icon"
                    app:layout_constraintTop_toBottomOf="@id/tv_update_title"
                    tools:text="版本 v1.2.0" />

                <!-- 更新标签 - 约束到右侧 -->
                <TextView
                    android:id="@+id/tv_update_badge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="18dp"
                    android:background="@drawable/beautiful_label_bg_orange"
                    android:fontFamily="sans-serif-medium"
                    android:paddingHorizontal="14dp"
                    android:paddingVertical="8dp"
                    android:text="NEW"
                    android:textColor="@color/white"
                    android:textSize="12dp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 分隔基准线 - 用于背景区域定位，不可见 -->
                <View
                    android:id="@+id/divider_update_header"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="18dp"
                    android:visibility="invisible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_update_version" />

                <!-- 更新内容 - 优化排版，约束到分隔线下方 -->
                <TextView
                    android:id="@+id/tv_update_content"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="18dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginBottom="20dp"
                    android:fontFamily="sans-serif"
                    android:lineSpacingExtra="7dp"
                    android:textColor="@color/field_content_color"
                    android:textSize="15dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider_update_header"
                    tools:text="• 修复了夜间模式下的显示问题\n• 优化了动画效果和流畅度\n• 提升了整体性能和稳定性\n• 新增了更多个性化选项" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 统一的二级页面操作栏 - 使用新的组件 -->
    <com.smartcar.easylauncher.shared.view.SecondaryPageActionBar
        android:id="@+id/action_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</com.smartcar.easylauncher.shared.view.easylayout.EasyConstraintLayout>
